import Constants from 'expo-constants';
import * as Linking from 'expo-linking';

/**
 * Get the current app's deep link scheme from configuration
 */
export const getDeepLinkScheme = (): string => {
  return Constants.expoConfig?.scheme as string;
};

/**
 * Get the base WebView URL from configuration
 */
export const getWebViewUrl = (): string => {
  // return Constants.expoConfig?.extra?.webViewUrl;
  return 'https://app.usemultiplier.com';
};

/**
 * Parse deep link URL and extract the path
 * Examples:
 * - multiplier-dev://dashboard → /dashboard
 * - multiplier://expenses/reports?filter=paid → /expenses/reports?filter=paid
 * - multiplier-prev:// → /
 */
export const parseDeepLinkPath = (deepLinkUrl: string): string | null => {
  try {
    const parsed = Linking.parse(deepLinkUrl);
    const currentScheme = getDeepLinkScheme();
    
    // Validate scheme matches current environment
    if (parsed.scheme !== currentScheme) {
      return null;
    }
    
    // Build path from hostname and path
    let fullPath = '';
    
    // Handle hostname (e.g., multiplier://dashboard -> hostname: "dashboard")
    if (parsed.hostname) {
      fullPath += `/${parsed.hostname}`;
    }
    
    // Handle path (e.g., multiplier://dashboard/settings -> path: "/settings")
    if (parsed.path) {
      const cleanPath = parsed.path.startsWith('/') ? parsed.path : `/${parsed.path}`;
      fullPath += cleanPath;
    }
    
    // Add query parameters if they exist
    if (parsed.queryParams && Object.keys(parsed.queryParams).length > 0) {
      const queryString = Object.entries(parsed.queryParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`)
        .join('&');
      fullPath += `?${queryString}`;
    }
    
    return fullPath || '/';
  } catch {
    return null;
  }
};

/**
 * Construct full WebView URL from a deep link path
 */
export const constructWebViewUrl = (path: string): string => {
  const baseUrl = getWebViewUrl();
  
  if (!baseUrl) {
    throw new Error('WebView base URL not configured');
  }
  
  // Ensure baseUrl doesn't end with '/'
  const cleanBaseUrl = baseUrl.replace(/\/$/, '');
  
  // Ensure path starts with '/'
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  
  return `${cleanBaseUrl}${cleanPath}`;
};

/**
 * Parse deep link and return the full WebView URL
 * This is the main function that combines parsing and URL construction
 */
export const parseDeepLinkToWebViewUrl = (deepLinkUrl: string): string | null => {
  const path = parseDeepLinkPath(deepLinkUrl);
  
  if (!path) {
    return null;
  }
  
  return constructWebViewUrl(path);
};

/**
 * Check if the current URL matches the base domain
 */
export const isSameDomain = (url: string): boolean => {
  try {
    const baseUrl = getWebViewUrl();
    if (!baseUrl) return false;

    const baseDomain = new URL(baseUrl).hostname;
    const urlDomain = new URL(url).hostname;
    return baseDomain === urlDomain;
  } catch {
    return false;
  }
};

/**
 * Check if a URL is an OAuth-related URL
 */
export const isOAuthUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    const pathname = urlObj.pathname.toLowerCase();
    const search = urlObj.search.toLowerCase();

    // Check for OAuth-related domains
    const oauthDomains = [
      'accounts.google.com',
      'oauth2.googleapis.com',
      'login.microsoftonline.com',
      'login.live.com',
      'github.com',
      'api.github.com',
      'auth0.com',
      'okta.com'
    ];

    if (oauthDomains.some(domain => hostname.includes(domain))) {
      return true;
    }

    // Check for OAuth-related paths and parameters
    const oauthIndicators = [
      'oauth',
      'auth',
      'login',
      'callback',
      'authorize',
      'token',
      'sso'
    ];

    return oauthIndicators.some(indicator =>
      pathname.includes(indicator) || search.includes(indicator)
    );
  } catch {
    return false;
  }
};

/**
 * Check if a URL might be causing a blank screen (common OAuth callback issue)
 */
export const isBlankScreenUrl = (url: string): boolean => {
  if (!url) return true;

  const blankUrls = [
    'about:blank',
    'data:text/html',
    '',
    'null'
  ];

  return blankUrls.some(blankUrl => url === blankUrl || url.startsWith(blankUrl));
};
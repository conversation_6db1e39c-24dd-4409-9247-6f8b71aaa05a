export const iosZoomPreventionScript = `
  (function() {
    // iOS-specific zoom prevention - minimal intervention
    if (window.webkit && window.webkit.messageHandlers) {
      // Only add a style to ensure zoom is locked
      const style = document.createElement('style');
      style.textContent = \`
        html, body {
          -webkit-text-size-adjust: none !important;
          zoom: 1 !important;
        }
      \`;
      document.head.appendChild(style);
    }
  })();
`;

export const androidZoomPreventionScript = `
  (function() {
    // Android-specific zoom prevention - minimal intervention
    if (window.Android || navigator.userAgent.includes('Android')) {
      // Just ensure zoom controls are hidden
      const style = document.createElement('style');
      style.textContent = \`
        [class*="zoom"], [id*="zoom"] {
          display: none !important;
        }
      \`;
      document.head.appendChild(style);
    }
  })();
`;

export const getPlatformSpecificScript = (platform: string) => {
  switch (platform) {
    case 'ios':
      return iosZoomPreventionScript;
    case 'android':
      return androidZoomPreventionScript;
    default:
      return '';
  }
}; 
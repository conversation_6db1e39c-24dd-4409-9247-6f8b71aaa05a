const preventZoomAndFitContent = `
  (function() {
    // === VIEWPORT META TAG SETUP ===
    function setupViewport() {
      // Remove existing viewport meta tags
      const existingMetas = document.querySelectorAll('meta[name="viewport"]');
      existingMetas.forEach(meta => meta.remove());

      // Add comprehensive viewport meta tag
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover';
      document.head.appendChild(meta);
    }

    // === CSS-ONLY ZOOM PREVENTION ===
    function injectStyles() {
      const style = document.createElement('style');
      style.textContent = \`
        /* Comprehensive zoom prevention through CSS only */
        html {
          -webkit-text-size-adjust: 100% !important;
          -ms-text-size-adjust: 100% !important;
          text-size-adjust: 100% !important;
          touch-action: manipulation !important;
          zoom: 1 !important;
          -webkit-user-zoom: fixed !important;
          -moz-user-zoom: fixed !important;
          user-zoom: fixed !important;
        }

        body {
          -webkit-text-size-adjust: 100% !important;
          -ms-text-size-adjust: 100% !important;
          text-size-adjust: 100% !important;
          touch-action: manipulation !important;
          margin: 0 !important;
          padding: 0 !important;
          overflow-x: hidden !important;
          zoom: 1 !important;
        }

        /* Apply touch-action to all elements */
        * {
          touch-action: manipulation !important;
          max-width: 100vw !important;
          box-sizing: border-box !important;
        }

        /* Prevent zoom on input focus - key for mobile */
        input, textarea, select {
          font-size: 16px !important;
          transform-origin: left top !important;
          zoom: 1 !important;
        }

        input:focus, textarea:focus, select:focus {
          font-size: 16px !important;
          transform: scale(1) !important;
          zoom: 1 !important;
        }

        /* Responsive media */
        img, video, canvas, svg {
          max-width: 100% !important;
          height: auto !important;
        }

        /* Prevent text selection on non-interactive elements */
        body, div, p, span {
          -webkit-user-select: none !important;
          user-select: none !important;
        }

        /* Allow text selection for interactive elements */
        input, textarea, [contenteditable], button, a {
          -webkit-user-select: text !important;
          user-select: text !important;
        }

        /* Additional zoom locks */
        @media screen {
          html, body {
            zoom: 1 !important;
            -webkit-text-size-adjust: 100% !important;
          }
        }
      \`;
      document.head.appendChild(style);
    }

    // === MINIMAL SETUP ===
    function init() {
      setupViewport();
      injectStyles();

      // Set font size on existing inputs without event listeners
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.style.fontSize = '16px';
      });

      // Handle dynamically added inputs with minimal observer
      if (window.MutationObserver) {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) {
                  const newInputs = node.querySelectorAll ? node.querySelectorAll('input, textarea, select') : [];
                  newInputs.forEach((input) => {
                    input.style.fontSize = '16px';
                  });
                }
              });
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }

    // Run initialization
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  })();
`;

const fileDownloadBridge = `
  (function() {
    // File download and viewing bridge for React Native WebView

    // Add download and file viewing functions to window
    window.downloadFile = function(url, filename, contentType) {
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        const message = {
          type: 'DOWNLOAD_FILE',
          payload: {
            url: url,
            filename: filename,
            contentType: contentType
          }
        };
        window.ReactNativeWebView.postMessage(JSON.stringify(message));
        return true; // Indicate that download was handled
      }
      return false; // Indicate that download was not handled
    };

    window.openFile = function(url, filename, contentType) {
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        const message = {
          type: 'OPEN_FILE',
          payload: {
            url: url,
            filename: filename,
            contentType: contentType
          }
        };
        window.ReactNativeWebView.postMessage(JSON.stringify(message));
        return true; // Indicate that file viewing was handled
      }
      return false; // Indicate that file viewing was not handled
    };

    // Make functions globally available
    if (typeof window !== 'undefined') {
      window.multiplierApp = {
        downloadFile: window.downloadFile,
        openFile: window.openFile,
        isReactNativeApp: true
      };
    }
  })();
`;

// Consolidated WebView script - single export for injection
export const consolidatedWebViewScript = `
  ${preventZoomAndFitContent}
  ${fileDownloadBridge}
`;

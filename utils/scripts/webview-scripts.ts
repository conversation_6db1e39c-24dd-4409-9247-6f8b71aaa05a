const preventZoomAndFitContent = `
  (function() {
    // === VIEWPORT META TAG SETUP ===
    function setupViewport() {
      // Remove existing viewport meta tags
      const existingMetas = document.querySelectorAll('meta[name="viewport"]');
      existingMetas.forEach(meta => meta.remove());

      // Add comprehensive viewport meta tag
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, shrink-to-fit=no, viewport-fit=cover';
      document.head.appendChild(meta);
    }

    // === CSS-ONLY ZOOM PREVENTION ===
    function injectStyles() {
      const style = document.createElement('style');
      style.textContent = \`
        /* Comprehensive zoom prevention through CSS only */
        html {
          -webkit-text-size-adjust: 100% !important;
          -ms-text-size-adjust: 100% !important;
          text-size-adjust: 100% !important;
          touch-action: manipulation !important;
          zoom: 1 !important;
          -webkit-user-zoom: fixed !important;
          -moz-user-zoom: fixed !important;
          user-zoom: fixed !important;
        }

        body {
          -webkit-text-size-adjust: 100% !important;
          -ms-text-size-adjust: 100% !important;
          text-size-adjust: 100% !important;
          touch-action: manipulation !important;
          margin: 0 !important;
          padding: 0 !important;
          overflow-x: hidden !important;
          zoom: 1 !important;
        }

        /* Apply touch-action to all elements */
        * {
          touch-action: manipulation !important;
          max-width: 100vw !important;
          box-sizing: border-box !important;
        }

        /* Prevent zoom on input focus - key for mobile */
        input, textarea, select {
          font-size: 16px !important;
          transform-origin: left top !important;
          zoom: 1 !important;
        }

        input:focus, textarea:focus, select:focus {
          font-size: 16px !important;
          transform: scale(1) !important;
          zoom: 1 !important;
        }

        /* Responsive media */
        img, video, canvas, svg {
          max-width: 100% !important;
          height: auto !important;
        }

        /* Prevent text selection on non-interactive elements */
        body, div, p, span {
          -webkit-user-select: none !important;
          user-select: none !important;
        }

        /* Allow text selection for interactive elements */
        input, textarea, [contenteditable], button, a {
          -webkit-user-select: text !important;
          user-select: text !important;
        }

        /* Additional zoom locks */
        @media screen {
          html, body {
            zoom: 1 !important;
            -webkit-text-size-adjust: 100% !important;
          }
        }
      \`;
      document.head.appendChild(style);
    }

    // === MINIMAL SETUP ===
    function init() {
      setupViewport();
      injectStyles();

      // Set font size on existing inputs without event listeners
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.style.fontSize = '16px';
      });

      // Handle dynamically added inputs with minimal observer
      if (window.MutationObserver) {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) {
                  const newInputs = node.querySelectorAll ? node.querySelectorAll('input, textarea, select') : [];
                  newInputs.forEach((input) => {
                    input.style.fontSize = '16px';
                  });
                }
              });
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }

    // Run initialization
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
    } else {
      init();
    }
  })();
`;

const fileDownloadBridge = `
  (function() {
    // File download and viewing bridge for React Native WebView

    // Add download and file viewing functions to window
    window.downloadFile = function(url, filename, contentType) {
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        const message = {
          type: 'DOWNLOAD_FILE',
          payload: {
            url: url,
            filename: filename,
            contentType: contentType
          }
        };
        window.ReactNativeWebView.postMessage(JSON.stringify(message));
        return true; // Indicate that download was handled
      }
      return false; // Indicate that download was not handled
    };

    window.openFile = function(url, filename, contentType) {
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        const message = {
          type: 'OPEN_FILE',
          payload: {
            url: url,
            filename: filename,
            contentType: contentType
          }
        };
        window.ReactNativeWebView.postMessage(JSON.stringify(message));
        return true; // Indicate that file viewing was handled
      }
      return false; // Indicate that file viewing was not handled
    };

    // Make functions globally available
    if (typeof window !== 'undefined') {
      window.multiplierApp = {
        downloadFile: window.downloadFile,
        openFile: window.openFile,
        isReactNativeApp: true
      };
    }
  })();
`;

const oauthCallbackHandler = `
  (function() {
    // OAuth callback handler for React Native WebView

    // Function to detect if current page is an OAuth callback
    function isOAuthCallback() {
      const url = window.location.href.toLowerCase();
      const pathname = window.location.pathname.toLowerCase();
      const search = window.location.search.toLowerCase();

      // Check for OAuth-related indicators
      const oauthIndicators = [
        'oauth',
        'auth',
        'login',
        'callback',
        'authorize',
        'token',
        'sso'
      ];

      return oauthIndicators.some(indicator =>
        url.includes(indicator) || pathname.includes(indicator) || search.includes(indicator)
      );
    }

    // Function to detect Google popup OAuth
    function isGooglePopupOAuth() {
      const url = window.location.href;
      const urlObj = new URL(url);
      return urlObj.hostname.includes('accounts.google.com') &&
             (urlObj.searchParams.get('ux_mode') === 'popup' ||
              urlObj.searchParams.get('ui_mode') === 'card' ||
              urlObj.searchParams.get('redirect_uri') === 'gis_transform');
    }

    // Function to handle OAuth callback completion
    function handleOAuthCallback() {
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        const message = {
          type: 'oauth_callback',
          payload: {
            url: window.location.href,
            pathname: window.location.pathname,
            search: window.location.search,
            hash: window.location.hash,
            timestamp: Date.now()
          }
        };
        window.ReactNativeWebView.postMessage(JSON.stringify(message));
      }
    }

    // Handle Google popup OAuth by converting to redirect mode
    if (isGooglePopupOAuth()) {
      console.log('Google popup OAuth detected, attempting to convert to redirect mode');

      // Try to modify the OAuth flow to use redirect instead of popup
      try {
        const currentUrl = new URL(window.location.href);
        const origin = currentUrl.searchParams.get('origin') || 'https://app.usemultiplier.com';

        // Remove popup-specific parameters
        currentUrl.searchParams.delete('ux_mode');
        currentUrl.searchParams.delete('ui_mode');
        currentUrl.searchParams.delete('redirect_uri'); // Remove the gis_transform redirect

        // Set redirect mode parameters with correct redirect URI
        currentUrl.searchParams.set('ux_mode', 'redirect');
        currentUrl.searchParams.set('redirect_uri', origin + '/auth/callback');

        // Redirect to the modified URL
        console.log('Redirecting to modified OAuth URL:', currentUrl.toString());
        console.log('Using redirect URI:', origin + '/auth/callback');
        window.location.href = currentUrl.toString();
        return;
      } catch (error) {
        console.error('Failed to modify Google OAuth URL:', error);
      }
    }

    // Monitor for OAuth callbacks
    if (isOAuthCallback()) {
      console.log('OAuth callback detected:', window.location.href);
      handleOAuthCallback();

      // Also handle any redirects that might happen
      const originalPushState = history.pushState;
      const originalReplaceState = history.replaceState;

      history.pushState = function() {
        originalPushState.apply(this, arguments);
        if (isOAuthCallback()) {
          handleOAuthCallback();
        }
      };

      history.replaceState = function() {
        originalReplaceState.apply(this, arguments);
        if (isOAuthCallback()) {
          handleOAuthCallback();
        }
      };
    }

    // Handle page visibility changes (useful for detecting when OAuth popup closes)
    document.addEventListener('visibilitychange', function() {
      if (!document.hidden && isOAuthCallback()) {
        handleOAuthCallback();
      }
    });

    // Handle focus events (another way to detect OAuth completion)
    window.addEventListener('focus', function() {
      if (isOAuthCallback()) {
        handleOAuthCallback();
      }
    });
  })();
`;

const googleOAuthFallback = `
  (function() {
    // Google OAuth fallback for WebView - replaces GIS with standard OAuth 2.0

    function createStandardOAuthUrl(clientId, redirectUri) {
      const params = new URLSearchParams({
        client_id: clientId,
        redirect_uri: redirectUri,
        response_type: 'code',
        scope: 'openid email profile',
        access_type: 'offline',
        prompt: 'select_account'
      });

      return 'https://accounts.google.com/oauth/authorize?' + params.toString();
    }

    // Intercept Google Sign-In button clicks
    function interceptGoogleSignIn() {
      document.addEventListener('click', function(event) {
        const target = event.target;

        // Check if it's a Google Sign-In button or related element
        if (target && (
          target.getAttribute('data-callback') ||
          target.classList.contains('g_id_signin') ||
          target.closest('[data-callback]') ||
          target.closest('.g_id_signin') ||
          (target.textContent && target.textContent.toLowerCase().includes('google'))
        )) {
          console.log('Google Sign-In button clicked, using standard OAuth flow');

          // Extract client ID from current page or use default
          const clientId = '**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com';
          const redirectUri = window.location.origin + '/auth/callback';

          // Prevent default action
          event.preventDefault();
          event.stopPropagation();

          // Redirect to standard OAuth flow
          const oauthUrl = createStandardOAuthUrl(clientId, redirectUri);
          console.log('Redirecting to standard OAuth:', oauthUrl);
          window.location.href = oauthUrl;

          return false;
        }
      }, true); // Use capture phase to intercept early
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', interceptGoogleSignIn);
    } else {
      interceptGoogleSignIn();
    }
  })();
`;

const googleOAuthInterceptor = `
  (function() {
    // Google OAuth interceptor for WebView compatibility

    // Override Google Identity Services to force redirect mode
    function interceptGoogleOAuth() {
      // Intercept Google Identity Services initialization
      if (window.google && window.google.accounts) {
        const originalInit = window.google.accounts.id.initialize;
        window.google.accounts.id.initialize = function(config) {
          console.log('Intercepting Google OAuth initialization');

          // Force redirect mode for WebView compatibility
          if (config) {
            config.ux_mode = 'redirect';
            // Use the app's origin for redirect URI
            const appOrigin = document.referrer ? new URL(document.referrer).origin : window.location.origin;
            config.redirect_uri = appOrigin + '/auth/callback';
            delete config.callback; // Remove popup callback
            console.log('Setting redirect URI to:', config.redirect_uri);
          }

          return originalInit.call(this, config);
        };
      }

      // Intercept any popup-based OAuth attempts
      const originalOpen = window.open;
      window.open = function(url, name, features) {
        if (url && url.includes('accounts.google.com')) {
          console.log('Intercepting Google OAuth popup, redirecting instead');

          // If it's a Google OAuth URL, modify it to use proper redirect
          try {
            const oauthUrl = new URL(url);
            if (oauthUrl.searchParams.get('redirect_uri') === 'gis_transform') {
              // Replace GIS transform with proper redirect URI
              const appOrigin = document.referrer ? new URL(document.referrer).origin : 'https://app.usemultiplier.com';
              oauthUrl.searchParams.set('redirect_uri', appOrigin + '/auth/callback');
              oauthUrl.searchParams.set('ux_mode', 'redirect');
              console.log('Modified OAuth URL:', oauthUrl.toString());
              window.location.href = oauthUrl.toString();
              return null;
            }
          } catch (error) {
            console.error('Failed to modify OAuth popup URL:', error);
          }

          window.location.href = url;
          return null;
        }
        return originalOpen.call(this, url, name, features);
      };
    }

    // Run interceptor when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', interceptGoogleOAuth);
    } else {
      interceptGoogleOAuth();
    }

    // Also run when Google scripts load
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === 1 && node.tagName === 'SCRIPT') {
            if (node.src && node.src.includes('accounts.google.com')) {
              console.log('Google OAuth script detected, setting up interceptor');
              setTimeout(interceptGoogleOAuth, 100);
            }
          }
        });
      });
    });

    observer.observe(document.head, { childList: true, subtree: true });
  })();
`;

// Consolidated WebView script - single export for injection
export const consolidatedWebViewScript = `
  ${preventZoomAndFitContent}
  ${fileDownloadBridge}
  ${oauthCallbackHandler}
  ${googleOAuthFallback}
  ${googleOAuthInterceptor}
`;

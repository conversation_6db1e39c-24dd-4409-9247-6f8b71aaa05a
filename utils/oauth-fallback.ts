import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { Platform } from 'react-native';

/**
 * Alternative OAuth implementation using expo-web-browser
 * Use this as a fallback if WebView OAuth continues to fail
 */

// Configure WebBrowser for OAuth
WebBrowser.maybeCompleteAuthSession();

interface OAuthResult {
  type: 'success' | 'cancel' | 'error';
  url?: string;
  error?: string;
}

/**
 * Handle OAuth using external browser (fallback approach)
 * This opens OAuth in the system browser instead of WebView
 */
export const handleOAuthWithBrowser = async (
  oauthUrl: string,
  redirectUrl: string = 'https://app.usemultiplier.com/auth/callback'
): Promise<OAuthResult> => {
  try {
    console.log('🔐 Starting OAuth with external browser:', oauthUrl);
    
    const result = await WebBrowser.openAuthSessionAsync(
      oauthUrl,
      redirectUrl,
      {
        // iOS specific options
        ...(Platform.OS === 'ios' && {
          preferEphemeralSession: false, // Allow cookies to persist
          showInRecents: false,
        }),
        // Android specific options
        ...(Platform.OS === 'android' && {
          showTitle: true,
          toolbarColor: '#f76918', // Multiplier brand color
          secondaryToolbarColor: '#ffffff',
          enableUrlBarHiding: true,
          enableDefaultShare: false,
        }),
      }
    );

    console.log('🔐 OAuth browser result:', result);

    if (result.type === 'success') {
      return {
        type: 'success',
        url: result.url,
      };
    } else if (result.type === 'cancel') {
      return {
        type: 'cancel',
      };
    } else {
      return {
        type: 'error',
        error: 'OAuth session failed',
      };
    }
  } catch (error) {
    console.error('❌ OAuth browser error:', error);
    return {
      type: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Extract authentication data from OAuth callback URL
 */
export const parseOAuthCallback = (url: string): {
  success: boolean;
  token?: string;
  error?: string;
  code?: string;
  state?: string;
} => {
  try {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    const hash = urlObj.hash.substring(1);
    const hashParams = new URLSearchParams(hash);

    // Check for error first
    const error = params.get('error') || hashParams.get('error');
    if (error) {
      return {
        success: false,
        error: error,
      };
    }

    // Look for access token (implicit flow)
    const accessToken = params.get('access_token') || hashParams.get('access_token');
    if (accessToken) {
      return {
        success: true,
        token: accessToken,
      };
    }

    // Look for authorization code (authorization code flow)
    const code = params.get('code');
    const state = params.get('state');
    if (code) {
      return {
        success: true,
        code: code,
        state: state || undefined,
      };
    }

    return {
      success: false,
      error: 'No authentication data found in callback URL',
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to parse callback URL',
    };
  }
};

/**
 * Complete OAuth flow using external browser
 * This is a complete alternative to WebView OAuth
 */
export const completeOAuthFlow = async (
  baseUrl: string = 'https://app.usemultiplier.com'
): Promise<{
  success: boolean;
  token?: string;
  error?: string;
}> => {
  try {
    // Construct OAuth URL (this should match your web app's OAuth initiation)
    const oauthUrl = `${baseUrl}/auth/google?mobile=true`;
    const redirectUrl = `${baseUrl}/auth/callback`;

    console.log('🔐 Starting complete OAuth flow');
    console.log('OAuth URL:', oauthUrl);
    console.log('Redirect URL:', redirectUrl);

    const result = await handleOAuthWithBrowser(oauthUrl, redirectUrl);

    if (result.type === 'success' && result.url) {
      const authData = parseOAuthCallback(result.url);
      
      if (authData.success) {
        console.log('✅ OAuth completed successfully');
        return {
          success: true,
          token: authData.token,
        };
      } else {
        console.error('❌ OAuth callback parsing failed:', authData.error);
        return {
          success: false,
          error: authData.error,
        };
      }
    } else if (result.type === 'cancel') {
      console.log('🚫 OAuth cancelled by user');
      return {
        success: false,
        error: 'OAuth cancelled by user',
      };
    } else {
      console.error('❌ OAuth failed:', result.error);
      return {
        success: false,
        error: result.error || 'OAuth failed',
      };
    }
  } catch (error) {
    console.error('❌ Complete OAuth flow error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Check if OAuth should use external browser fallback
 * You can implement logic here to determine when to use fallback
 */
export const shouldUseOAuthFallback = (): boolean => {
  // For now, return false to use WebView by default
  // Set to true to always use external browser
  // You could also check for specific conditions like:
  // - Previous OAuth failures
  // - User preference
  // - Platform-specific issues
  return false;
};

/**
 * Usage example:
 * 
 * // In your component
 * const handleLogin = async () => {
 *   if (shouldUseOAuthFallback()) {
 *     const result = await completeOAuthFlow();
 *     if (result.success) {
 *       // Handle successful authentication
 *       console.log('Token:', result.token);
 *     } else {
 *       // Handle error
 *       console.error('OAuth failed:', result.error);
 *     }
 *   } else {
 *     // Use WebView OAuth (current implementation)
 *   }
 * };
 */

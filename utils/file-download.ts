import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

/**
 * Production-ready file download utility that gracefully handles missing native modules
 */

/**
 * Safe module loaders that never throw errors
 */
const safeLoadMediaLibrary = async (): Promise<any> => {
  // In development builds without native modules, don't even try to load
  // This prevents ANY native module errors
  try {
    // First check if we're in a development environment without native modules
    // by testing if the module exists without calling any functions
    const MediaLibrary = await import('expo-media-library');
    const module = MediaLibrary.default || MediaLibrary;

    // Only proceed if the module has the expected structure
    if (module && typeof module.getPermissionsAsync === 'function') {
      // Don't test the function - just return it if it exists
      // The actual test will happen when we use it with proper error handling
      console.log('✅ expo-media-library module available');
      return module;
    } else {
      console.log('📱 expo-media-library module structure not available');
      return null;
    }
  } catch {
    console.log('📱 expo-media-library not available in this build');
    return null;
  }
};

const safeLoadSharing = async (): Promise<any> => {
  try {
    const Sharing = await import('expo-sharing');
    const module = Sharing.default || Sharing;

    // Only proceed if the module has the expected structure
    if (module && typeof module.isAvailableAsync === 'function') {
      console.log('✅ expo-sharing module available');
      return module;
    } else {
      console.log('📱 expo-sharing module structure not available');
      return null;
    }
  } catch {
    console.log('📱 expo-sharing not available in this build');
    return null;
  }
};

export interface DownloadMessage {
  type: 'DOWNLOAD_FILE' | 'OPEN_FILE';
  payload: {
    url: string;
    filename: string;
    contentType?: string;
  };
}

export interface FileViewMessage {
  type: 'OPEN_FILE';
  payload: {
    url: string;
    filename: string;
    contentType?: string;
  };
}

/**
 * Get file extension from filename or URL
 */
export const getFileExtension = (filename: string): string => {
  const parts = filename.split('.');
  return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
};

/**
 * Determine if file can be previewed in-app
 */
export const canPreviewFile = (contentType?: string, filename?: string): boolean => {
  if (!contentType && !filename) return false;
  
  const extension = filename ? getFileExtension(filename) : '';
  
  // Check by content type
  if (contentType) {
    if (contentType.includes('pdf')) return true;
    if (contentType.includes('image/')) return true;
  }
  
  // Check by extension
  const previewableExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
  return previewableExtensions.includes(extension);
};

/**
 * Get appropriate MIME type for file
 */
export const getMimeType = (filename: string, contentType?: string): string => {
  if (contentType) return contentType;
  
  const extension = getFileExtension(filename);
  const mimeTypes: Record<string, string> = {
    pdf: 'application/pdf',
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    zip: 'application/zip',
  };
  
  return mimeTypes[extension] || 'application/octet-stream';
};

/**
 * Download file to device storage
 */
export const downloadFile = async (
  url: string,
  filename: string,
  contentType?: string
): Promise<{ success: boolean; localUri?: string; error?: string }> => {
  try {
    console.log('📥 Starting file download:', { url, filename, contentType });
    
    // Create download directory if it doesn't exist
    const downloadDir = `${FileSystem.documentDirectory}downloads/`;
    const dirInfo = await FileSystem.getInfoAsync(downloadDir);
    if (!dirInfo.exists) {
      await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
    }
    
    // Generate unique filename to avoid conflicts
    const timestamp = Date.now();
    const extension = getFileExtension(filename);
    const baseName = filename.replace(`.${extension}`, '');
    const uniqueFilename = `${baseName}_${timestamp}.${extension}`;
    const localUri = `${downloadDir}${uniqueFilename}`;
    
    // Download the file
    const downloadResult = await FileSystem.downloadAsync(url, localUri);
    
    if (downloadResult.status !== 200) {
      throw new Error(`Download failed with status: ${downloadResult.status}`);
    }
    
    console.log('✅ File downloaded successfully to:', downloadResult.uri);
    console.log('📁 File location details:', {
      downloadDir,
      filename: uniqueFilename,
      fullPath: downloadResult.uri,
      originalFilename: filename,
    });
    
    // For images, try to save to photo library (zero-error approach)
    if (contentType?.includes('image/') || ['jpg', 'jpeg', 'png', 'gif'].includes(getFileExtension(filename))) {
      // Skip photo library in development builds to avoid any native module errors
      if (__DEV__) {
        console.log('📸 Development mode - photo library disabled, file saved to downloads folder');
      } else {
        const MediaLibraryModule = await safeLoadMediaLibrary();
        if (MediaLibraryModule) {
          try {
            const { status } = await MediaLibraryModule.requestPermissionsAsync();
            if (status === 'granted') {
              await MediaLibraryModule.saveToLibraryAsync(downloadResult.uri);
              console.log('📸 Image saved to photo library');
            } else {
              console.log('📸 Photo library permission not granted');
            }
          } catch {
            console.log('📸 Could not save to photo library - continuing with file download');
          }
        } else {
          console.log('📸 Photo library not available - file saved to downloads folder');
        }
      }
    }
    
    return { success: true, localUri: downloadResult.uri };
  } catch (error) {
    console.error('❌ File download failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown download error' 
    };
  }
};

/**
 * Share downloaded file using native sharing
 */
export const shareFile = async (localUri: string, filename: string): Promise<boolean> => {
  // Skip sharing in development builds to avoid any native module errors
  if (__DEV__) {
    console.log('📤 Development mode - sharing disabled, file saved to:', localUri);
    return false;
  }
  
  const SharingModule = await safeLoadSharing();
  if (SharingModule) {
    try {
      const isAvailable = await SharingModule.isAvailableAsync();
      if (isAvailable) {
        await SharingModule.shareAsync(localUri, {
          mimeType: getMimeType(filename),
          dialogTitle: `Share ${filename}`,
        });
        console.log('📤 File shared successfully');
        return true;
      } else {
        console.log('📤 Sharing not available on this device - file saved to:', localUri);
        return false;
      }
    } catch {
      console.log('📤 Sharing failed - file saved to:', localUri);
      return false;
    }
  } else {
    console.log('📤 Sharing not available - file saved to:', localUri);
    return false;
  }
};

/**
 * Handle download message from WebView
 */
export const handleDownloadMessage = async (message: DownloadMessage): Promise<void> => {
  const { url, filename, contentType } = message.payload;
  
  try {
    // Show loading indicator
    console.log('📥 Processing download request:', filename);
    
    // Download the file
    const result = await downloadFile(url, filename, contentType);
    
    if (result.success && result.localUri) {
      // Show success message and offer to share
      Alert.alert(
        'Download Complete',
        `${filename} has been downloaded successfully.`,
        [
          { text: 'OK', style: 'default' },
          { 
            text: 'Share', 
            style: 'default',
            onPress: () => shareFile(result.localUri!, filename)
          }
        ]
      );
    } else {
      // Show error message
      Alert.alert(
        'Download Failed',
        result.error || 'Unable to download file. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  } catch (error) {
    console.error('❌ Error handling download message:', error);
    Alert.alert(
      'Download Error',
      'An unexpected error occurred while downloading the file.',
      [{ text: 'OK', style: 'default' }]
    );
  }
};

/**
 * Get local file info if it exists
 */
export const getLocalFileInfo = async (
  _url: string,
  filename: string
): Promise<{ exists: boolean; localUri?: string; size?: number }> => {
  try {
    const downloadDir = `${FileSystem.documentDirectory}downloads/`;
    const files = await FileSystem.readDirectoryAsync(downloadDir);

    // Look for files with the same base name
    const baseName = filename.replace(/\.[^/.]+$/, '');
    const matchingFile = files.find(file => file.startsWith(baseName));

    if (matchingFile) {
      const localUri = `${downloadDir}${matchingFile}`;
      const info = await FileSystem.getInfoAsync(localUri);
      return {
        exists: info.exists,
        localUri: info.exists ? localUri : undefined,
        size: info.exists ? info.size : undefined,
      };
    }

    return { exists: false };
  } catch (error) {
    console.error('❌ Error checking local file:', error);
    return { exists: false };
  }
};

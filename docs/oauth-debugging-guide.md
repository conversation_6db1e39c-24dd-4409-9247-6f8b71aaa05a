# Google SSO OAuth Debugging Guide

## 🚨 Issue Description
Google SSO login shows a blank page in the React Native WebView after entering email and password, while it works fine in the mobile browser.

## 🔧 Fixes Applied

### 1. **Popup to Redirect Conversion**
- **Problem**: Google SSO uses `ux_mode=popup` which doesn't work in WebViews
- **Solution**: JavaScript injection to convert popup mode to redirect mode
- **Implementation**: Override `google.accounts.id.initialize` and `google.accounts.oauth2.initTokenClient`

### 2. **User Agent Compatibility**
- **Problem**: WebView user agent might be blocked by Google
- **Solution**: Use Safari-like user agent
- **Implementation**: `Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1 Multiplier/1.0`

### 3. **Cookie and Storage Settings**
- **Problem**: OAuth requires cookies to work properly
- **Solution**: Enable cookie and storage settings
- **Implementation**: 
  - `sharedCookiesEnabled: true`
  - `thirdPartyCookiesEnabled: true`
  - `domStorageEnabled: true`
  - `databaseEnabled: true`

### 4. **Navigation Policy**
- **Problem**: OAuth URLs might be blocked by navigation policy
- **Solution**: Allow Google OAuth URLs
- **Implementation**: Enhanced `onShouldStartLoadWithRequest` logic

### 5. **JavaScript Injection**
- **Problem**: OAuth flows require JavaScript and proper event handling
- **Solution**: Comprehensive JavaScript injection
- **Implementation**: OAuth-specific event listeners and message handling

## 🧪 Testing Steps

### 1. **Test the Updated App**
```bash
# The app is already installed on your device
# Open the app and try Google SSO login
```

### 2. **Monitor Console Logs**
Look for these log messages during OAuth flow:
- `🔐 Google accounts API detected`
- `🔐 Converted Google One Tap from popup to redirect mode`
- `🔐 Converted Google OAuth from popup to redirect mode`
- `🔐 Allowing Google OAuth URL: [URL]`
- `🔐 OAuth redirect detected: [URL]`
- `🔐 OAuth form submit detected: [URL]`

### 3. **Expected Behavior**
1. **Before Fix**: Blank page after entering credentials
2. **After Fix**: Should redirect properly and complete authentication

### 4. **Debug Commands**
```bash
# Run the app with debugging
npx expo run:ios --device

# Test OAuth configuration
node scripts/test-oauth-config.js

# Monitor logs in real-time
npx expo logs
```

## 🔍 Troubleshooting

### If Still Blank Page:

1. **Check Console Logs**
   - Look for `🔐` messages
   - Check for any error messages
   - Verify OAuth URLs are being allowed

2. **Verify JavaScript Injection**
   - Check if `window.google` is detected
   - Verify popup to redirect conversion is working
   - Ensure OAuth event listeners are active

3. **Test URL Allowance**
   - Verify Google OAuth URLs are not being blocked
   - Check navigation policy is working correctly

4. **Check Cookie Settings**
   - Ensure cookies are being properly handled
   - Verify storage is enabled

### Common Issues:

1. **Popup Still Not Working**
   - JavaScript injection might not be working
   - Check if `window.google.accounts` exists
   - Verify the override functions are being called

2. **Navigation Blocked**
   - OAuth URLs might still be blocked
   - Check `onShouldStartLoadWithRequest` logic
   - Verify URL patterns are correct

3. **Cookies Not Working**
   - Check if `sharedCookiesEnabled` is true
   - Verify `thirdPartyCookiesEnabled` is enabled
   - Test with different cookie settings

## 📊 Debug Information

### OAuth URL Patterns Allowed:
- `accounts.google.com`
- `google.com/oauth`
- `google.com/gsi`
- `googleusercontent.com`

### WebView Configuration:
- User Agent: Safari-like
- Cookies: Enabled
- JavaScript: Enabled
- Storage: Enabled
- Navigation: OAuth URLs allowed

### JavaScript Injection Features:
- Window.open override
- Google One Tap popup to redirect
- Google OAuth2 popup to redirect
- Form submission monitoring
- Iframe load monitoring
- Message handling

## 🎯 Next Steps

1. **Test the updated app** on your device
2. **Monitor console logs** for OAuth messages
3. **Verify the flow** works end-to-end
4. **Report any issues** with specific error messages

## 📞 Support

If the issue persists:
1. Share console logs with `🔐` messages
2. Provide the exact URL that shows blank page
3. Include any error messages from the console
4. Test with different Google accounts if possible 
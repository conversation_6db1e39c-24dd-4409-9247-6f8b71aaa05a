# OAuth Debugging Guide for React Native WebView

## Overview

This guide helps debug Google SSO OAuth callback issues in the React Native WebView implementation. The blank screen issue typically occurs when OAuth callbacks are not properly handled by the WebView.

## Recent Changes Made

### 1. Enhanced Navigation Restrictions (`app/_layout.tsx`)

- Added OAuth-specific URL detection and allowlisting
- Enhanced logging for navigation events
- Added error handling for WebView load failures
- Implemented blank screen detection and recovery

### 2. OAuth Utility Functions (`utils/deep-linking.ts`)

- `isOAuthUrl()`: Detects OAuth-related URLs
- `isBlankScreenUrl()`: Identifies blank screen conditions
- Enhanced domain and path pattern matching

### 3. WebView Script Enhancements (`utils/scripts/webview-scripts.ts`)

- Added OAuth callback detection script
- Implemented callback message posting to React Native
- Added visibility and focus event handlers

### 4. WebView Configuration Improvements

- Enhanced cookie and session handling
- Disabled security restrictions that might block OAuth
- Added platform-specific OAuth support settings

## Debugging Steps

### Step 1: Enable Development Logging

Run the app in development mode to see detailed logs:

```bash
yarn start
# Then press 'i' for iOS or 'a' for Android
```

Look for these log messages:
- `🔗 Navigation request:` - Shows all navigation attempts
- `🔐 OAuth URL navigation allowed:` - Confirms OAuth URLs are allowed
- `⚠️ Blank page detected:` - Identifies blank screen issues
- `🔄 Attempting to reload after OAuth blank screen` - Shows recovery attempts

### Step 2: Test OAuth Flow

1. Open the app
2. Navigate to login page
3. Tap "Sign in with Google"
4. Complete OAuth flow in browser
5. Monitor logs for issues

### Step 3: Common Issues and Solutions

#### Issue: Navigation Blocked
**Symptoms**: OAuth redirects fail, "Navigation blocked" logs
**Solution**: Check if OAuth domains are in the allowlist

#### Issue: Blank Screen After Callback
**Symptoms**: White/blank screen after OAuth completion
**Solution**: The app now auto-detects and reloads blank screens

#### Issue: Cookies Not Persisted
**Symptoms**: Login doesn't persist, repeated login prompts
**Solution**: Enhanced cookie settings are now enabled

### Step 4: Manual Testing Checklist

- [ ] OAuth login initiates correctly
- [ ] Google OAuth page loads properly
- [ ] User can complete authentication
- [ ] Callback redirects back to app
- [ ] No blank screen appears
- [ ] User is logged in successfully
- [ ] Session persists on app restart

## Configuration Verification

### WebView Settings

Verify these settings are enabled in `_layout.tsx`:

```typescript
// Android
domStorageEnabled: true,
thirdPartyCookiesEnabled: true,
sharedCookiesEnabled: true,
incognito: false,

// iOS
sharedCookiesEnabled: true,
limitsNavigationsToAppBoundDomains: false,
fraudulentWebsiteWarningEnabled: false,
```

### OAuth Domains

Current allowlisted domains:
- accounts.google.com
- oauth2.googleapis.com
- login.microsoftonline.com
- login.live.com
- github.com
- api.github.com
- auth0.com
- okta.com

## Advanced Debugging

### Enable WebView Debugging

For deeper inspection, enable WebView debugging:

#### iOS
1. Open Safari
2. Go to Develop menu
3. Select your device and app
4. Inspect WebView content

#### Android
1. Enable USB debugging
2. Open Chrome
3. Go to `chrome://inspect`
4. Select your WebView

### Network Monitoring

Monitor network requests to identify failed OAuth calls:
- Use Charles Proxy or similar
- Look for failed redirects
- Check for blocked domains

## Troubleshooting

### If OAuth Still Fails

1. **Check Web App Configuration**
   - Verify OAuth redirect URIs include your domain
   - Ensure CORS settings allow your app

2. **Test in Browser**
   - Open `https://app.usemultiplier.com` in mobile browser
   - Complete OAuth flow to verify web app works

3. **Alternative Approaches**
   - Consider using `expo-web-browser` for OAuth
   - Implement custom URL scheme handling
   - Use deep linking for OAuth callbacks

### Emergency Fallback

If OAuth continues to fail, implement a fallback:

```typescript
// In WebView onError handler
onError={(syntheticEvent) => {
  const { nativeEvent } = syntheticEvent;
  if (nativeEvent.url.includes('oauth') || nativeEvent.url.includes('auth')) {
    // Fallback to external browser
    Linking.openURL(nativeEvent.url);
  }
}}
```

## Next Steps

1. Test the current implementation
2. Monitor logs during OAuth flow
3. Report specific error messages if issues persist
4. Consider implementing expo-web-browser as alternative

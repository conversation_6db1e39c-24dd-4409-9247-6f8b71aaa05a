# OAuth Callback Handler Implementation

## Overview

Since your web app now uses standard OAuth 2.0 for WebView, you need to create a callback handler at `/auth/callback` to process the authorization code returned by Google.

## Frontend Callback Handler (React)

Create a new component or route handler for `/auth/callback`:

```typescript
// AuthCallback.tsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

interface AuthCallbackProps {
  onAuthSuccess: (user: any) => void;
  onAuthError: (error: string) => void;
}

const AuthCallback: React.FC<AuthCallbackProps> = ({ onAuthSuccess, onAuthError }) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');

  useEffect(() => {
    const processCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        // Check for OAuth error
        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        // Verify state parameter
        const storedState = sessionStorage.getItem('oauth_state');
        if (!state || state !== storedState) {
          throw new Error('Invalid state parameter - possible CSRF attack');
        }

        // Clear stored state
        sessionStorage.removeItem('oauth_state');
        const oauthAction = sessionStorage.getItem('oauth_action');
        sessionStorage.removeItem('oauth_action');

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange authorization code for tokens
        const tokenResponse = await fetch('/api/auth/google/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code,
            redirectUri: window.location.origin + '/auth/callback',
            action: oauthAction, // 'signup_with' or 'signin_with'
          }),
        });

        if (!tokenResponse.ok) {
          const errorData = await tokenResponse.json();
          throw new Error(errorData.message || 'Token exchange failed');
        }

        const authData = await tokenResponse.json();
        
        setStatus('success');
        onAuthSuccess(authData.user);
        
        // Redirect to dashboard or intended page
        navigate('/dashboard');

      } catch (error) {
        console.error('OAuth callback error:', error);
        setStatus('error');
        onAuthError(error instanceof Error ? error.message : 'Authentication failed');
        
        // Redirect to login page after error
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    processCallback();
  }, [searchParams, navigate, onAuthSuccess, onAuthError]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        {status === 'processing' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Completing sign in...</p>
          </>
        )}
        {status === 'success' && (
          <>
            <div className="text-green-600 text-4xl mb-4">✓</div>
            <p>Sign in successful! Redirecting...</p>
          </>
        )}
        {status === 'error' && (
          <>
            <div className="text-red-600 text-4xl mb-4">✗</div>
            <p>Sign in failed. Redirecting to login...</p>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthCallback;
```

## Backend API Handler

Create an API endpoint to handle the token exchange:

```typescript
// /api/auth/google/callback
import { Request, Response } from 'express';

interface TokenExchangeRequest {
  code: string;
  redirectUri: string;
  action: 'signup_with' | 'signin_with';
}

export const handleGoogleCallback = async (req: Request, res: Response) => {
  try {
    const { code, redirectUri, action }: TokenExchangeRequest = req.body;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    // Exchange authorization code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.VITE_GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!, // You'll need this
        code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      throw new Error(`Token exchange failed: ${errorData.error_description}`);
    }

    const tokens = await tokenResponse.json();

    // Get user information
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokens.access_token}`,
      },
    });

    if (!userResponse.ok) {
      throw new Error('Failed to fetch user information');
    }

    const googleUser = await userResponse.json();

    // Process user based on action (signup vs signin)
    let user;
    if (action === 'signup_with') {
      // Handle signup logic
      user = await createOrUpdateUser(googleUser);
    } else {
      // Handle signin logic
      user = await findOrCreateUser(googleUser);
    }

    // Create session or JWT token
    const sessionToken = generateSessionToken(user); // Implement this

    // Set session cookie or return token
    res.cookie('session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    });

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        picture: user.picture,
      },
    });

  } catch (error) {
    console.error('Google OAuth callback error:', error);
    res.status(500).json({
      message: error instanceof Error ? error.message : 'Authentication failed',
    });
  }
};

// Helper functions (implement based on your user model)
async function createOrUpdateUser(googleUser: any) {
  // Your user creation/update logic
  return {
    id: googleUser.id,
    email: googleUser.email,
    name: googleUser.name,
    picture: googleUser.picture,
  };
}

async function findOrCreateUser(googleUser: any) {
  // Your user lookup/creation logic
  return {
    id: googleUser.id,
    email: googleUser.email,
    name: googleUser.name,
    picture: googleUser.picture,
  };
}

function generateSessionToken(user: any): string {
  // Your session token generation logic
  return 'session-token';
}
```

## Route Configuration

Add the callback route to your router:

```typescript
// In your React Router configuration
import AuthCallback from './components/AuthCallback';

const router = createBrowserRouter([
  // ... other routes
  {
    path: '/auth/callback',
    element: <AuthCallback 
      onAuthSuccess={(user) => {
        // Handle successful authentication
        console.log('User authenticated:', user);
      }}
      onAuthError={(error) => {
        // Handle authentication error
        console.error('Auth error:', error);
      }}
    />,
  },
]);
```

## Environment Variables

Make sure you have these environment variables:

```env
# Frontend (.env)
VITE_GOOGLE_CLIENT_ID=2004387095-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com

# Backend (.env)
VITE_GOOGLE_CLIENT_ID=2004387095-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Google OAuth Console Configuration

Ensure your Google OAuth Console has this redirect URI:
```
https://app.usemultiplier.com/auth/callback
```

## Testing

1. **WebView Test**: The modified GoogleButton will detect WebView and use standard OAuth
2. **Browser Test**: Regular browsers will continue using Google Identity Services
3. **Callback Test**: The `/auth/callback` endpoint will process the authorization code

This implementation provides a seamless experience for both WebView and browser environments while maintaining security with state parameter validation.

# OAuth Redirect URI Configuration for React Native WebView

## Overview

This document outlines the proper OAuth redirect URI configuration for your React Native app using WebView to serve `https://app.usemultiplier.com`.

## Current Configuration Analysis

### App Configuration
- **Base URL**: `https://app.usemultiplier.com`
- **Deep Link Schemes**: 
  - Production: `multiplier://`
  - Preview: `multiplier-prev://`
  - Development: `multiplier-dev://`

### WebView Context
Your app loads the web application inside a WebView, which means OAuth callbacks need to work within this context.

## OAuth Redirect URI Requirements

### 1. Web Application OAuth Configuration

Your web application (`https://app.usemultiplier.com`) should have these redirect URIs configured in Google OAuth Console:

```
https://app.usemultiplier.com/auth/callback
https://app.usemultiplier.com/auth/google/callback
https://app.usemultiplier.com/oauth/callback
```

### 2. React Native Deep Link Support (Optional)

If you want to support direct deep link callbacks, also add:

```
multiplier://auth/callback
multiplier-prev://auth/callback
multiplier-dev://auth/callback
```

## Google OAuth Console Configuration

### Step 1: Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Credentials"

### Step 2: Configure OAuth 2.0 Client
1. Find your OAuth 2.0 Client ID
2. Click "Edit" 
3. Add Authorized Redirect URIs:

**Required URIs:**
```
https://app.usemultiplier.com/auth/callback
https://app.usemultiplier.com/auth/google/callback
https://app.usemultiplier.com/login/callback
https://app.usemultiplier.com/oauth/callback
```

**Optional Deep Link URIs:**
```
multiplier://auth/callback
multiplier-prev://auth/callback  
multiplier-dev://auth/callback
```

### Step 3: Authorized JavaScript Origins
Add these origins:
```
https://app.usemultiplier.com
```

## Web Application Code Requirements

### 1. OAuth Callback Handler

Your web application needs to handle OAuth callbacks properly. Example:

```javascript
// In your web app's OAuth callback handler
function handleOAuthCallback() {
  // Check if running in React Native WebView
  if (window.isReactNativeApp) {
    // Post message to React Native
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'oauth_success',
        payload: {
          token: 'your-auth-token',
          user: 'user-data'
        }
      }));
    }
  }
  
  // Continue with normal web flow
  // Redirect to dashboard or handle authentication
}
```

### 2. OAuth Initialization

When starting OAuth flow in WebView context:

```javascript
// Detect WebView context
const isWebView = window.isReactNativeApp || 
                  navigator.userAgent.includes('ReactNativeWebView');

// Configure OAuth with appropriate redirect URI
const redirectUri = isWebView 
  ? 'https://app.usemultiplier.com/auth/callback'
  : window.location.origin + '/auth/callback';

// Start OAuth flow
const oauthUrl = `https://accounts.google.com/oauth/authorize?` +
  `client_id=${clientId}&` +
  `redirect_uri=${encodeURIComponent(redirectUri)}&` +
  `response_type=code&` +
  `scope=openid email profile`;

window.location.href = oauthUrl;
```

## Testing OAuth Configuration

### 1. Test Web App Directly
1. Open `https://app.usemultiplier.com` in mobile browser
2. Complete OAuth flow
3. Verify callback works correctly

### 2. Test in React Native WebView
1. Run your React Native app
2. Navigate to login
3. Complete OAuth flow
4. Check for blank screens or errors

### 3. Debug OAuth Flow

Use browser developer tools to monitor:
- Network requests during OAuth
- Console errors
- Redirect chain
- Final callback URL

## Common Issues and Solutions

### Issue 1: Redirect URI Mismatch
**Error**: "redirect_uri_mismatch"
**Solution**: Ensure exact match between configured URI and actual callback URI

### Issue 2: Blank Screen After Callback
**Cause**: OAuth callback doesn't handle WebView context
**Solution**: Implement WebView-specific callback handling (already added to your app)

### Issue 3: CORS Errors
**Cause**: Cross-origin restrictions
**Solution**: Configure CORS headers on your web server

### Issue 4: Cookie Issues
**Cause**: Third-party cookie restrictions
**Solution**: Use first-party cookies or session storage

## Recommended OAuth Flow for WebView

### Option 1: Standard Web Flow (Current)
1. User taps "Sign in with Google" in WebView
2. WebView navigates to Google OAuth
3. User completes authentication
4. Google redirects to your web app callback
5. Web app handles authentication and redirects to dashboard

### Option 2: Hybrid Flow (Alternative)
1. User taps "Sign in with Google"
2. Open OAuth in external browser using `expo-web-browser`
3. Handle callback via deep link
4. Return to WebView with authentication token

### Option 3: Custom URL Scheme (Advanced)
1. Configure custom URL scheme callbacks
2. Handle OAuth entirely through deep links
3. Pass authentication data back to WebView

## Implementation Status

✅ **Completed**:
- Enhanced WebView OAuth URL handling
- Added OAuth callback detection
- Implemented blank screen recovery
- Enhanced logging and debugging

🔄 **Next Steps**:
1. Verify OAuth redirect URIs in Google Console
2. Test OAuth flow with current implementation
3. Monitor logs for specific error messages
4. Consider alternative approaches if needed

## Verification Checklist

- [ ] Google OAuth Console has correct redirect URIs
- [ ] Web application handles WebView context
- [ ] OAuth callback includes WebView detection
- [ ] CORS headers configured correctly
- [ ] Cookie settings allow authentication
- [ ] Deep link schemes registered (if using)
- [ ] Error handling implemented
- [ ] Logging enabled for debugging

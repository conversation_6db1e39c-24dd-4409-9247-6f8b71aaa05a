# OAuth Redirect URI Fix

## Issue Analysis

The HTTP 400 error shows that the OAuth interceptor is working (we can see `ux_mode=redirect` in the URL), but the redirect URI is incorrect:

**Problem**: `redirect_uri=https%3A%2F%2Faccounts.google.com%2Fauth%2Fcallback`
**Should be**: `redirect_uri=https%3A%2F%2Fapp.usemultiplier.com%2Fauth%2Fcallback`

## Root Cause

Google Identity Services (GIS) uses a special `gis_transform` redirect that only works in browser popup contexts. When we convert it to redirect mode, we need to replace this with a proper callback URL.

## Solutions Implemented

### 1. Enhanced URL Modification
The interceptor now:
- Extracts the origin from the OAuth request
- Removes the `gis_transform` redirect URI
- Sets proper redirect URI: `https://app.usemultiplier.com/auth/callback`

### 2. Standard OAuth 2.0 Fallback
Added a click interceptor that:
- Detects Google Sign-In button clicks
- Replaces GIS flow with standard OAuth 2.0
- Uses proper redirect-based authentication

### 3. Multiple Interception Points
- URL parameter modification during navigation
- Google Identity Services initialization override
- Click event interception for sign-in buttons
- Window.open override for popup attempts

## Testing Steps

### 1. Test Current Implementation
Run your app and try Google OAuth. Look for these log messages:

```
✅ Success indicators:
- "Intercepting Google OAuth initialization"
- "Google Sign-In button clicked, using standard OAuth flow"
- "Redirecting to standard OAuth: https://accounts.google.com/oauth/authorize?..."
- "Using redirect URI: https://app.usemultiplier.com/auth/callback"

❌ Problem indicators:
- HTTP 400 errors
- redirect_uri still pointing to accounts.google.com
- "gis_transform" still in URLs
```

### 2. Verify Redirect URI Configuration

Ensure your Google OAuth Console has this redirect URI configured:
```
https://app.usemultiplier.com/auth/callback
```

### 3. Check Web App Callback Handler

Your web application needs to handle the OAuth callback at `/auth/callback`. Verify this endpoint exists and processes the authorization code.

## If Issues Persist

### Option 1: Force Standard OAuth Flow

Add this to your web application to completely bypass GIS:

```html
<script>
// Replace Google Identity Services with standard OAuth
if (window.isReactNativeApp) {
  window.addEventListener('load', function() {
    // Remove all GIS elements
    document.querySelectorAll('[data-callback], .g_id_signin').forEach(el => {
      el.addEventListener('click', function(e) {
        e.preventDefault();
        const clientId = '**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com';
        const redirectUri = window.location.origin + '/auth/callback';
        const oauthUrl = `https://accounts.google.com/oauth/authorize?` +
          `client_id=${clientId}&` +
          `redirect_uri=${encodeURIComponent(redirectUri)}&` +
          `response_type=code&` +
          `scope=openid email profile&` +
          `access_type=offline&` +
          `prompt=select_account`;
        window.location.href = oauthUrl;
      });
    });
  });
}
</script>
```

### Option 2: Use External Browser OAuth

If WebView OAuth continues to fail, implement the external browser approach:

```typescript
// In your React Native component
import { completeOAuthFlow } from '@/utils/oauth-fallback';

const handleGoogleLogin = async () => {
  try {
    const result = await completeOAuthFlow();
    if (result.success) {
      console.log('OAuth completed successfully');
      // Reload WebView to reflect authentication
      webViewRef.current?.reload();
    } else {
      console.error('OAuth failed:', result.error);
    }
  } catch (error) {
    console.error('OAuth error:', error);
  }
};
```

### Option 3: Debug Network Requests

Enable network debugging to see the exact OAuth flow:

1. **iOS**: Safari Developer Tools → Develop → [Your Device] → [Your App]
2. **Android**: Chrome → `chrome://inspect` → Select your WebView

Monitor:
- Initial OAuth request
- Redirect attempts
- Final callback URL
- Any 400/401/403 errors

## Expected OAuth Flow

### Correct Flow:
1. User clicks "Sign in with Google"
2. Interceptor detects click
3. Redirects to: `https://accounts.google.com/oauth/authorize?client_id=...&redirect_uri=https://app.usemultiplier.com/auth/callback`
4. User completes authentication
5. Google redirects to: `https://app.usemultiplier.com/auth/callback?code=...`
6. Your web app processes the authorization code
7. User is logged in

### Current Problem Flow:
1. User clicks "Sign in with Google"
2. GIS tries to use popup mode
3. WebView converts to redirect mode
4. But redirect_uri is still `gis_transform` or points to Google
5. Results in 400 error

## Verification Checklist

- [ ] Google OAuth Console has correct redirect URI
- [ ] Web app has `/auth/callback` endpoint
- [ ] WebView interceptors are working (check logs)
- [ ] No more `gis_transform` in URLs
- [ ] redirect_uri points to your domain
- [ ] Standard OAuth 2.0 flow is used instead of GIS

## Next Steps

1. **Test the enhanced interceptors** - They should now set the correct redirect URI
2. **Monitor the logs** - Look for the success indicators listed above
3. **Check the OAuth URL** - It should use standard OAuth 2.0 parameters
4. **Verify callback handling** - Ensure your web app can process the authorization code

The key fix is ensuring the redirect URI points to your domain (`https://app.usemultiplier.com/auth/callback`) instead of Google's domain or the special `gis_transform` value.

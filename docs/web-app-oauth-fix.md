# Web Application OAuth Fix for WebView

## Problem Analysis

The logs show that Google's GSI `/gsi/select` endpoint returns a 400 error when used with `ux_mode=redirect`. This endpoint is designed only for popup/iframe flows and doesn't support redirect mode.

**Root Issue**: Google Identity Services (GIS) is fundamentally incompatible with WebView environments.

## Solution: Modify Your Web Application

### Step 1: Add WebView Detection

Add this to your web application's HTML head:

```html
<script>
// Detect WebView environment
window.isWebView = window.isReactNativeApp || 
                   navigator.userAgent.includes('ReactNativeWebView') ||
                   navigator.userAgent.includes('wv') ||
                   window.webkit?.messageHandlers;

console.log('WebView detected:', window.isWebView);
</script>
```

### Step 2: Conditional OAuth Implementation

Replace your current Google Sign-In implementation with this:

```javascript
function initializeGoogleAuth() {
  const clientId = '2004387095-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com';
  
  if (window.isWebView) {
    console.log('Using standard OAuth 2.0 for WebView');
    setupStandardOAuth(clientId);
  } else {
    console.log('Using Google Identity Services for browser');
    setupGoogleIdentityServices(clientId);
  }
}

// Standard OAuth 2.0 for WebView
function setupStandardOAuth(clientId) {
  const signInButtons = document.querySelectorAll('[data-google-signin], .google-signin-btn, #google-signin');
  
  signInButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();
      
      const redirectUri = window.location.origin + '/auth/callback';
      const state = generateRandomState(); // Implement this function
      
      // Store state in sessionStorage for verification
      sessionStorage.setItem('oauth_state', state);
      
      const oauthUrl = 'https://accounts.google.com/oauth/authorize?' + new URLSearchParams({
        client_id: clientId,
        redirect_uri: redirectUri,
        response_type: 'code',
        scope: 'openid email profile',
        access_type: 'offline',
        prompt: 'select_account',
        state: state
      });
      
      console.log('Redirecting to OAuth:', oauthUrl);
      window.location.href = oauthUrl;
    });
  });
}

// Google Identity Services for browsers
function setupGoogleIdentityServices(clientId) {
  google.accounts.id.initialize({
    client_id: clientId,
    callback: handleCredentialResponse
  });
  
  google.accounts.id.renderButton(
    document.getElementById('google-signin'),
    { theme: 'outline', size: 'large' }
  );
}

function handleCredentialResponse(response) {
  // Handle GIS response for browsers
  console.log('GIS response:', response);
  // Process the credential response
}

function generateRandomState() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeGoogleAuth);
```

### Step 3: Create OAuth Callback Handler

Create or update your `/auth/callback` endpoint to handle the authorization code:

```javascript
// Example callback handler (adjust for your backend framework)
app.get('/auth/callback', async (req, res) => {
  const { code, state } = req.query;
  
  // Verify state parameter
  const storedState = req.session.oauth_state || sessionStorage.getItem('oauth_state');
  if (state !== storedState) {
    return res.status(400).send('Invalid state parameter');
  }
  
  try {
    // Exchange authorization code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_id: 'your-client-id',
        client_secret: 'your-client-secret',
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: req.protocol + '://' + req.get('host') + '/auth/callback'
      })
    });
    
    const tokens = await tokenResponse.json();
    
    // Get user info
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: { Authorization: `Bearer ${tokens.access_token}` }
    });
    
    const user = await userResponse.json();
    
    // Store user session and redirect to dashboard
    req.session.user = user;
    res.redirect('/dashboard');
    
  } catch (error) {
    console.error('OAuth callback error:', error);
    res.status(500).send('Authentication failed');
  }
});
```

### Step 4: Update Your HTML

Replace your current Google Sign-In button with:

```html
<!-- Remove existing GIS script and div -->
<!-- <script src="https://accounts.google.com/gsi/client" async defer></script> -->
<!-- <div id="g_id_onload" data-client_id="..." data-callback="..."></div> -->

<!-- Replace with standard button -->
<button id="google-signin" class="google-signin-btn" data-google-signin>
  Sign in with Google
</button>
```

## Alternative: Use External Browser OAuth

If modifying the web app is not feasible, use the external browser approach:

```typescript
// In your React Native component
import { completeOAuthFlow } from '@/utils/oauth-fallback';

const handleGoogleLogin = async () => {
  try {
    console.log('Starting external browser OAuth');
    const result = await completeOAuthFlow('https://app.usemultiplier.com');
    
    if (result.success) {
      console.log('OAuth completed successfully');
      // Reload WebView to reflect authentication
      webViewRef.current?.reload();
    } else {
      console.error('OAuth failed:', result.error);
      Alert.alert('Login Failed', result.error || 'Authentication failed');
    }
  } catch (error) {
    console.error('OAuth error:', error);
    Alert.alert('Login Error', 'An error occurred during login');
  }
};

// Add a button in your React Native UI
<TouchableOpacity onPress={handleGoogleLogin}>
  <Text>Sign in with Google (External)</Text>
</TouchableOpacity>
```

## Testing the Fix

### Web App Changes:
1. Add WebView detection script
2. Implement conditional OAuth logic
3. Create/update OAuth callback handler
4. Test in both WebView and browser

### External Browser Approach:
1. Import the OAuth fallback utility
2. Add external login button
3. Test the external browser flow

## Recommendation

**Modify the web application** - This provides the best user experience and keeps the OAuth flow within the WebView. The external browser approach works but requires users to switch between apps.

The key insight is that Google Identity Services simply cannot work in WebView environments, so we need to use standard OAuth 2.0 when WebView is detected.

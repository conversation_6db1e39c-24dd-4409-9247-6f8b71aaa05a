# Google OAuth Popup Issue in WebView

## Root Cause Analysis

Based on your logs, the issue is that your web application is using **Google Identity Services (GIS) with popup mode**, which doesn't work in WebView environments.

### Key Evidence from Logs:
- `ux_mode=popup` - Expects to open a popup window
- `ui_mode=card` - New Google Identity Services flow
- `redirect_uri=gis_transform` - Special GIS redirect that only works in browsers

### Why This Fails in WebView:
1. **No Popup Support**: WebViews can't open popup windows like browsers
2. **GIS Transform**: The `gis_transform` redirect is a Google-specific mechanism that doesn't work in WebView
3. **PostMessage API**: GIS relies on `window.postMessage` between popup and parent, which fails in WebView

## Solutions Implemented

### 1. Enhanced Detection and Logging
- Added `isGooglePopupOAuth()` function to detect problematic OAuth flows
- Enhanced logging to identify when popup OAuth is attempted
- Fixed blank screen detection to avoid false positives

### 2. JavaScript Interceptors
Added scripts to automatically convert popup OAuth to redirect OAuth:
- Intercepts Google Identity Services initialization
- Overrides `window.open` for OAuth popups
- Modifies OAuth URLs to use redirect mode

### 3. URL Parameter Modification
The WebView script now automatically:
- Removes `ux_mode=popup` and `ui_mode=card`
- Sets `ux_mode=redirect`
- Updates `redirect_uri` to a proper callback URL

## Recommended Long-term Solutions

### Option 1: Fix Web Application (Recommended)
Modify your web application to detect WebView and use redirect-based OAuth:

```javascript
// In your web app
function initializeGoogleOAuth() {
  const isWebView = window.isReactNativeApp || 
                    navigator.userAgent.includes('ReactNativeWebView');
  
  if (isWebView) {
    // Use redirect-based OAuth for WebView
    google.accounts.id.initialize({
      client_id: 'your-client-id',
      ux_mode: 'redirect',
      redirect_uri: window.location.origin + '/auth/callback'
    });
  } else {
    // Use popup-based OAuth for browsers
    google.accounts.id.initialize({
      client_id: 'your-client-id',
      callback: handleCredentialResponse
    });
  }
}
```

### Option 2: Use expo-web-browser (Alternative)
Implement OAuth using external browser instead of WebView:

```typescript
import { completeOAuthFlow } from '@/utils/oauth-fallback';

const handleLogin = async () => {
  const result = await completeOAuthFlow();
  if (result.success) {
    // Handle successful authentication
  }
};
```

### Option 3: Custom OAuth Implementation
Implement OAuth flow without Google Identity Services:

```javascript
// Direct OAuth 2.0 flow
const oauthUrl = `https://accounts.google.com/oauth/authorize?` +
  `client_id=${clientId}&` +
  `redirect_uri=${encodeURIComponent(redirectUri)}&` +
  `response_type=code&` +
  `scope=openid email profile&` +
  `access_type=offline`;

window.location.href = oauthUrl;
```

## Testing the Current Fix

1. **Test the interceptor**: The WebView script should now automatically convert popup OAuth to redirect OAuth
2. **Monitor logs**: Look for "Intercepting Google OAuth" messages
3. **Check behavior**: OAuth should now redirect instead of showing blank screen

## If Issues Persist

### Immediate Workaround
Add this to your web application to force redirect mode:

```html
<script>
// Force redirect mode for WebView
if (window.isReactNativeApp) {
  // Override Google Identity Services
  window.addEventListener('load', function() {
    if (window.google && window.google.accounts) {
      const originalInit = window.google.accounts.id.initialize;
      window.google.accounts.id.initialize = function(config) {
        config.ux_mode = 'redirect';
        config.redirect_uri = window.location.origin + '/auth/callback';
        delete config.callback;
        return originalInit.call(this, config);
      };
    }
  });
}
</script>
```

### Alternative: External Browser OAuth
If WebView OAuth continues to fail, use the external browser approach:

```typescript
// In your React Native component
import { shouldUseOAuthFallback, completeOAuthFlow } from '@/utils/oauth-fallback';

const handleGoogleLogin = async () => {
  if (shouldUseOAuthFallback()) {
    const result = await completeOAuthFlow();
    if (result.success) {
      // Reload WebView with authentication
      webViewRef.current?.reload();
    }
  } else {
    // Continue with WebView OAuth
  }
};
```

## Google OAuth Console Configuration

Ensure your Google OAuth console has these redirect URIs:

```
https://app.usemultiplier.com/auth/callback
https://app.usemultiplier.com/auth/google/callback
https://app.usemultiplier.com/login/callback
https://app.usemultiplier.com/oauth/callback
```

## Next Steps

1. **Test current implementation** - The interceptor should fix the popup issue
2. **Monitor logs** - Check for "Intercepting Google OAuth" messages
3. **Update web app** - Implement WebView detection for long-term solution
4. **Consider alternatives** - Use expo-web-browser if WebView OAuth remains problematic

The root cause is clear: Google's popup-based OAuth doesn't work in WebView. The interceptor should resolve this, but the best long-term solution is to modify your web application to use redirect-based OAuth when running in WebView context.

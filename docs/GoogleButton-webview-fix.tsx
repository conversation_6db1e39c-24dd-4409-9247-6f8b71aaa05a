import React, { useEffect, useState } from 'react';

import { Helmet, HelmetTags } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import tw from 'twin.macro';

import importMetaEnv from 'import-meta-env';

const googleUrl = 'https://accounts.google.com/gsi/client';

export interface GoogleCredentialResponse {
  credential: string;
}

// Helper function to detect WebView environment
const isWebView = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return !!(
    (window as any).isReactNativeApp ||
    navigator.userAgent.includes('ReactNativeWebView') ||
    navigator.userAgent.includes('wv') ||
    (window as any).webkit?.messageHandlers
  );
};

// Helper function to generate random state for OAuth security
const generateRandomState = (): string => {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
};

// Standard OAuth 2.0 redirect for WebView
const redirectToStandardOAuth = (text: 'signup_with' | 'signin_with') => {
  const clientId = importMetaEnv.VITE_GOOGLE_CLIENT_ID;
  const redirectUri = window.location.origin + '/auth/callback';
  const state = generateRandomState();
  
  // Store state for verification (you'll need this in your callback handler)
  sessionStorage.setItem('oauth_state', state);
  sessionStorage.setItem('oauth_action', text); // Store whether it's signup or signin
  
  const oauthUrl = 'https://accounts.google.com/oauth/authorize?' + new URLSearchParams({
    client_id: clientId,
    redirect_uri: redirectUri,
    response_type: 'code',
    scope: 'openid email profile',
    access_type: 'offline',
    prompt: 'select_account',
    state: state
  });
  
  console.log('WebView detected - redirecting to standard OAuth:', oauthUrl);
  window.location.href = oauthUrl;
};

const GoogleButton: React.FC<{
  text: 'signup_with' | 'signin_with';
  onResponse: (response: GoogleCredentialResponse) => void;
  loading?: boolean;
}> = ({ text, onResponse, loading }) => {
  const { i18n } = useTranslation('common');
  const [scriptLoaded, setScriptLoaded] = useState(
    typeof window !== 'undefined' && typeof window.google !== 'undefined',
  );
  const [isWebViewEnv, setIsWebViewEnv] = useState(false);
  const divRef = React.createRef<HTMLDivElement>();

  // Check if we're in WebView environment
  useEffect(() => {
    setIsWebViewEnv(isWebView());
  }, []);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleChangeClientState = (newState: any, addedTags: HelmetTags) => {
    if (addedTags && addedTags.scriptTags) {
      const foundScript = addedTags.scriptTags.find(
        ({ src }) => src === googleUrl,
      );
      if (foundScript) {
        foundScript.addEventListener('load', () => setScriptLoaded(true), {
          once: true,
        });
      }
    }
  };

  // Handle WebView button click
  const handleWebViewClick = () => {
    if (loading) return;
    redirectToStandardOAuth(text);
  };

  useEffect(() => {
    // Skip Google Identity Services initialization in WebView
    if (isWebViewEnv) {
      console.log('WebView detected - skipping Google Identity Services initialization');
      return;
    }

    if (scriptLoaded && divRef.current && window.google) {
      window.google.accounts?.id?.initialize({
        client_id: importMetaEnv.VITE_GOOGLE_CLIENT_ID,
        callback: onResponse,
        ux_mode: 'popup', // Keep popup mode for regular browsers
      });
      window.google.accounts?.id?.renderButton(divRef.current, {
        theme: 'outline',
        size: 'large',
        width: divRef.current.clientWidth,
        height: 40,
        locale: i18n.language,
        text,
      });
    }
  }, [scriptLoaded, divRef, onResponse, isWebViewEnv]);

  useEffect(
    () => () => {
      const googleScript = document.querySelector('script[id="google-sso"]');
      const googleLink = document.querySelector(
        'link[id="googleidentityservice"]',
      );

      try {
        googleScript?.remove();
        googleLink?.remove();
        setScriptLoaded(false);
      } catch (e) {
        // No need to catch
      }
    },
    [],
  );

  // Render custom button for WebView
  if (isWebViewEnv) {
    return (
      <button
        data-testid="google-button"
        onClick={handleWebViewClick}
        disabled={loading}
        css={[
          loading && tw`opacity-50`,
          tw`flex items-center w-full justify-center h-[40px] max-h-[40px] min-h-[40px]`,
          tw`border border-gray-300 rounded-md bg-white hover:bg-gray-50`,
          tw`text-gray-700 font-medium text-sm`,
          tw`transition-colors duration-200`,
          loading && tw`cursor-not-allowed`,
        ]}
      >
        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
        {text === 'signup_with' ? 'Sign up with Google' : 'Sign in with Google'}
      </button>
    );
  }

  // Render normal Google Identity Services button for browsers
  return (
    <>
      <Helmet onChangeClientState={handleChangeClientState}>
        <script src={googleUrl} async defer id="google-sso" />
      </Helmet>
      <div
        data-testid="google-button"
        ref={divRef}
        css={[
          loading && tw`opacity-50`,
          tw`flex items-center w-full justify-center h-[40px] max-h-[40px] min-h-[40px]`,
        ]}
      />
    </>
  );
};

export default GoogleButton;

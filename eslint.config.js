// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');
const checkFilePlugin = require('eslint-plugin-check-file');

module.exports = defineConfig([
  expoConfig,
  {
    ignores: ['dist/*'],
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      'check-file': checkFilePlugin,
    },
    rules: {
      // Enforce kebab-case for file names
      'check-file/filename-naming-convention': [
        'error',
        {
          '**/*.{js,jsx,ts,tsx}': 'KEBAB_CASE',
        },
        {
          ignoreMiddleExtensions: true,
        },
      ],
      // Enforce kebab-case for folder names
      'check-file/folder-naming-convention': [
        'error',
        {
          '*/': 'KEBAB_CASE',
        },
      ],
    },
  },
  {
    // Exempt Expo Router special files from kebab-case rule
    files: [
      '**/_layout.{js,jsx,ts,tsx}',
      '**/+not-found.{js,jsx,ts,tsx}',
      '**/+html.{js,jsx,ts,tsx}',
      '**/_sitemap.{js,jsx,ts,tsx}',
      '**/\\(*.{js,jsx,ts,tsx}',
      '**/\\[*.{js,jsx,ts,tsx}',
    ],
    rules: {
      'check-file/filename-naming-convention': 'off',
    },
  },
]);


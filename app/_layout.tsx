import {
  getWebViewUrl,
  isSameDomain,
  parseDeepLinkToWebViewUrl
} from "@/utils/deep-linking";
import * as Haptics from "expo-haptics";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect, useRef, useState } from "react";
import { BackHandler, Linking, Platform, StyleSheet } from "react-native";
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
} from "react-native-gesture-handler";
import { runOnJS } from "react-native-reanimated";
import { SafeAreaView } from "react-native-safe-area-context";
import { WebView, WebViewMessageEvent } from "react-native-webview";
import { FileViewer } from "../components/file-viewer";
import {
  DownloadMessage,
  FileViewMessage,
  handleDownloadMessage,
} from "../utils/file-download";
import { consolidatedWebViewScript } from "../utils/scripts/webview-scripts";
import { getPlatformSpecificScript } from "../utils/scripts/zoom-prevention";

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {
  // Handle any errors if splash screen is already hidden
});

// Helper function to check if a URL is OAuth-related
const isOAuthUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    const pathname = urlObj.pathname.toLowerCase();
    const search = urlObj.search.toLowerCase();

    // Check for OAuth-related domains
    const oauthDomains = [
      'accounts.google.com',
      'oauth2.googleapis.com',
      'login.microsoftonline.com',
      'login.live.com',
      'github.com',
      'api.github.com',
      'auth0.com',
      'okta.com'
    ];

    if (oauthDomains.some(domain => hostname.includes(domain))) {
      return true;
    }

    // Check for OAuth-related paths and parameters
    const oauthIndicators = [
      'oauth',
      'auth',
      'login',
      'callback',
      'authorize',
      'token',
      'sso'
    ];

    return oauthIndicators.some(indicator =>
      pathname.includes(indicator) || search.includes(indicator)
    );
  } catch {
    return false;
  }
};

// Helper function to check if URL might cause blank screen
const isBlankScreenUrl = (url: string): boolean => {
  if (!url) return true;

  const blankUrls = [
    'about:blank',
    'data:text/html',
    '',
    'null'
  ];

  return blankUrls.some(blankUrl => url === blankUrl || url.startsWith(blankUrl));
};

// Helper function to detect Google popup-based OAuth (problematic in WebView)
const isGooglePopupOAuth = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes('accounts.google.com') &&
           (urlObj.searchParams.get('ux_mode') === 'popup' ||
            urlObj.searchParams.get('ui_mode') === 'card' ||
            urlObj.searchParams.get('redirect_uri') === 'gis_transform');
  } catch {
    return false;
  }
};

export default function RootLayout() {
  const [isWebViewLoaded, setIsWebViewLoaded] = useState(false);
  const [currentUrl, setCurrentUrl] = useState<string>(getWebViewUrl());
  const [navigationState, setNavigationState] = useState({
    canGoBack: false,
    canGoForward: false,
    url: currentUrl,
  });
  const webViewRef = useRef<WebView>(null);

  // File viewer state
  const [fileViewerVisible, setFileViewerVisible] = useState(false);
  const [currentFile, setCurrentFile] = useState<{
    url: string;
    name: string;
    contentType?: string;
  } | null>(null);

  // Combine consolidated WebView script with platform-specific scripts
  const combinedWebViewScript = `
    ${consolidatedWebViewScript}
    ${getPlatformSpecificScript(Platform.OS)}
  `;

  // Handle WebView messages for file downloads, viewing, and navigation
  const handleWebViewMessage = async (event: WebViewMessageEvent) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);

      // Handle file download messages
      if (message.type === "DOWNLOAD_FILE") {
        await handleDownloadMessage(message as DownloadMessage);
      } else if (message.type === "OPEN_FILE") {
        const fileMessage = message as FileViewMessage;
        setCurrentFile({
          url: fileMessage.payload.url,
          name: fileMessage.payload.filename,
          contentType: fileMessage.payload.contentType,
        });
        setFileViewerVisible(true);
      } else if (message.type === "navigation" && __DEV__) {
        // Handle navigation messages for debugging
        console.log("🌐 Web navigation:", message.action, message.url);
      } else if (message.type === "oauth_callback") {
        // Handle OAuth callback messages
        if (__DEV__) {
          console.log("🔐 OAuth callback received:", message.payload);
        }
      }
    } catch {
      // Handle WebView message parsing errors silently
    }
  };

  // Handle initial deep link when app is opened from a deep link
  useEffect(() => {
    const handleInitialUrl = async () => {
      try {
        const initialUrl = await Linking.getInitialURL();
        if (initialUrl) {
          const webViewUrl = parseDeepLinkToWebViewUrl(initialUrl);
          if (webViewUrl) {
            setCurrentUrl(webViewUrl);
          }
        }
      } catch (error) {
        console.error("❌ Error handling initial URL:", error);
      }
    };

    handleInitialUrl();
  }, []);

  // Handle deep links when app is already running
  useEffect(() => {
    const handleDeepLink = (event: { url: string }) => {
      const webViewUrl = parseDeepLinkToWebViewUrl(event.url);
      if (webViewUrl) {
        setCurrentUrl(webViewUrl);
      }
    };

    const subscription = Linking.addEventListener("url", handleDeepLink);

    return () => {
      subscription?.remove();
    };
  }, []);

  // Handle Android back button
  useEffect(() => {
    const handleBackPress = () => {
      // Check if we're on the dashboard page - disable back navigation to prevent blank screen
      // The dashboard is a root-level page, so going back from here can cause issues
      const isOnDashboard =
        navigationState.url.includes("member/dashboard") ||
        navigationState.url.includes("/dashboard") ||
        navigationState.url.endsWith("/dashboard");

      if (isOnDashboard) {
        // Don't allow back navigation from dashboard
        if (__DEV__)
          console.log("🚫 Back navigation blocked - user is on dashboard");
        return true; // Prevent default behavior (dontexit app)
      }

      if (navigationState.canGoBack) {
        // Trigger haptic feedback
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

        webViewRef.current?.goBack();
        return true; // Prevent default behavior
      }
      return false; // Allow default behavior (exit app)
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBackPress
    );

    return () => backHandler.remove();
  }, [navigationState.canGoBack, navigationState.url]);

  // Add these handlers outside the gesture
  const handleBack = () => {
    // Check if we're on the dashboard page - disable back navigation to prevent blank screen
    // The dashboard is a root-level page, so going back from here can cause issues
    const isOnDashboard =
      navigationState.url.includes("member/dashboard") ||
      navigationState.url.includes("/dashboard") ||
      navigationState.url.endsWith("/dashboard");

    if (isOnDashboard) {
      // Don't allow back navigation from dashboard
      if (__DEV__)
        console.log("🚫 Back gesture blocked - user is on dashboard");
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    webViewRef.current?.goBack();
    if (__DEV__) console.log("👆 Back gesture detected - navigating back");
  };

  const handleForward = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    webViewRef.current?.goForward();
    if (__DEV__)
      console.log("👆 Forward gesture detected - navigating forward");
  };

  // iOS back/forward gesture handler
  const navigationGesture = Gesture.Pan()
    .activeOffsetX([-20, 20]) // Start gesture after 20px horizontal movement in either direction
    .failOffsetY([-20, 20]) // Fail if vertical movement exceeds 20px
    .onEnd((event) => {
      if (__DEV__) {
        console.log("👆 Gesture detected:", {
          translationX: event.translationX,
          canGoBack: navigationState.canGoBack,
          canGoForward: navigationState.canGoForward,
        });
      }

      // Check if it's a left-to-right swipe (back gesture)
      if (event.translationX > 50 && navigationState.canGoBack) {
        runOnJS(handleBack)();
      }
      // Check if it's a right-to-left swipe (forward gesture)
      else if (event.translationX < -50 && navigationState.canGoForward) {
        runOnJS(handleForward)();
      }
    });

  // Hide splash screen when WebView is loaded
  useEffect(() => {
    if (isWebViewLoaded) {
      SplashScreen.hideAsync().catch(() => {
        // Handle any errors if splash screen is already hidden
      });
    }
  }, [isWebViewLoaded]);

  const handleWebViewLoad = () => {
    setIsWebViewLoaded(true);
  };

  const handleNavigationStateChange = (navState: any) => {
    setNavigationState(navState);

    // Log navigation state changes in development
    if (__DEV__) {
      console.log("🧭 Navigation state changed:", {
        canGoBack: navState.canGoBack,
        canGoForward: navState.canGoForward,
        url: navState.url,
        loading: navState.loading,
        title: navState.title,
      });

      // Check for OAuth-related URLs
      if (navState.url) {
        if (isOAuthUrl(navState.url)) {
          console.log("🔐 OAuth-related navigation detected:", navState.url);

          // Check for Google popup OAuth (problematic in WebView)
          if (isGooglePopupOAuth(navState.url)) {
            console.log("⚠️ Google popup OAuth detected - this may not work in WebView:", navState.url);
            console.log("💡 Consider switching to redirect-based OAuth flow in your web app");
          }
        }

        // Check for actual blank or error pages (not just empty titles)
        if (isBlankScreenUrl(navState.url)) {
          console.log("⚠️ Blank page detected - possible OAuth callback issue:", {
            url: navState.url,
            title: navState.title,
            loading: navState.loading
          });

          // If we detect a blank screen during OAuth flow, try to reload
          if (isOAuthUrl(currentUrl) && !navState.loading) {
            console.log("🔄 Attempting to reload after OAuth blank screen");
            setTimeout(() => {
              webViewRef.current?.reload();
            }, 1000);
          }
        }
      }
    }
  };

  return (
    <GestureHandlerRootView style={styles.container}>
      <StatusBar style="dark" backgroundColor="#ffffff" translucent={false} />
      <SafeAreaView style={styles.safeArea} edges={["top"]}>
        <GestureDetector gesture={navigationGesture}>
          <WebView
            ref={webViewRef}
            source={{ uri: currentUrl }}
            userAgent="ReactNativeWebView/1.0 Multiplier/1.0 AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile Safari/604.1"
            style={styles.webview}
            bounces={false}
            scrollEnabled={true}
            scalesPageToFit={false}
            automaticallyAdjustContentInsets={false}
            contentInset={{ top: 0, left: 0, bottom: 0, right: 0 }}
            injectedJavaScript={combinedWebViewScript}
            onLoad={handleWebViewLoad}
            onLoadEnd={handleWebViewLoad}
            onLoadStart={(syntheticEvent) => {
              if (__DEV__) {
                console.log("🔄 WebView load started:", syntheticEvent.nativeEvent.url);
              }
            }}
            onError={(syntheticEvent) => {
              if (__DEV__) {
                console.error("❌ WebView error:", syntheticEvent.nativeEvent);
              }
            }}
            onHttpError={(syntheticEvent) => {
              if (__DEV__) {
                console.error("🌐 WebView HTTP error:", syntheticEvent.nativeEvent);
              }
            }}
            onNavigationStateChange={handleNavigationStateChange}
            onMessage={handleWebViewMessage}
            // Disable built-in back/forward navigation gestures since we implement our own
            allowsBackForwardNavigationGestures={false}
            // Improve performance
            cacheEnabled={true}
            cacheMode="LOAD_DEFAULT"
            // Minimal zoom prevention props
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            allowsLinkPreview={false}
            // Platform-specific minimal settings
            {...(Platform.OS === "android" && {
              domStorageEnabled: true,
              javaScriptEnabled: true,
              allowFileAccess: true,
              allowFileAccessFromFileURLs: true,
              allowUniversalAccessFromFileURLs: true,
              mixedContentMode: "compatibility",
              thirdPartyCookiesEnabled: true,
              saveFormDataDisabled: false,
              // Enhanced settings for OAuth support
              cacheEnabled: true,
              incognito: false, // Allow cookies and session storage for OAuth
              sharedCookiesEnabled: true,
            })}
            {...(Platform.OS === "ios" && {
              allowsInlineMediaPlayback: true,
              dataDetectorTypes: "none",
              mediaPlaybackRequiresUserAction: false,
              allowsAirPlayForMediaPlayback: false,
              allowsPictureInPictureMediaPlayback: false,
              allowsFullscreenVideo: true,
              // Enhanced settings for OAuth support
              limitsNavigationsToAppBoundDomains: false, // Allow OAuth redirects
              sharedCookiesEnabled: true,
              fraudulentWebsiteWarningEnabled: false, // Prevent OAuth interruptions
            })}
            // Inject platform info for web app
            injectedJavaScriptBeforeContentLoaded={`
              window.isReactNativeApp = true;
              window.appVersion = '1.0.0';
              window.platform = '${Platform.OS}';

              // Add navigation history tracking for better back gesture support
              window.addEventListener('popstate', function() {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'navigation',
                  action: 'popstate',
                  url: window.location.href
                }));
              });

              // Track pushstate and replacestate for better history management
              const originalPushState = history.pushState;
              const originalReplaceState = history.replaceState;

              history.pushState = function() {
                originalPushState.apply(this, arguments);
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'navigation',
                  action: 'pushstate',
                  url: window.location.href
                }));
              };

              history.replaceState = function() {
                originalReplaceState.apply(this, arguments);
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'navigation',
                  action: 'replacestate',
                  url: window.location.href
                }));
              };
            `}
            // Allow navigation within same domain and secure protocols only
            onShouldStartLoadWithRequest={(request) => {
              const url = request.url;

              // Log navigation attempts for debugging OAuth issues
              if (__DEV__) {
                console.log("🔗 Navigation request:", url);
              }

              // Allow same domain navigation
              if (isSameDomain(url)) {
                if (__DEV__) {
                  console.log("✅ Same domain navigation allowed:", url);
                }
                return true;
              }

              // Allow OAuth-related URLs
              if (isOAuthUrl(url)) {
                if (__DEV__) {
                  console.log("🔐 OAuth URL navigation allowed:", url);
                }
                return true;
              }

              // Allow secure HTTPS URLs
              if (url.startsWith("https://")) {
                if (__DEV__) {
                  console.log("🔒 HTTPS navigation allowed:", url);
                }
                return true;
              }

              // Allow localhost for development
              if (__DEV__ && url.startsWith("http://localhost")) {
                console.log("🏠 Localhost navigation allowed:", url);
                return true;
              }

              // Block navigation and log for debugging
              if (__DEV__) {
                console.log("🚫 Navigation blocked:", url);
              }
              return false;
            }}
          />
        </GestureDetector>
      </SafeAreaView>

      {/* File Viewer Modal */}
      {currentFile && (
        <FileViewer
          visible={fileViewerVisible}
          onClose={() => {
            setFileViewerVisible(false);
            setCurrentFile(null);
          }}
          fileUrl={currentFile.url}
          fileName={currentFile.name}
          contentType={currentFile.contentType}
        />
      )}
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
  },
  safeArea: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
});

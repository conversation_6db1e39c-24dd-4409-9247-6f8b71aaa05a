{"name": "multiplier-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "lint": "expo lint", "development:build": "npx eas build --platform all -p ios --profile development", "preview:build": "npx eas build --platform all --profile preview", "preview:build:ios": "npx eas build --platform ios --profile preview", "preview:build:android": "npx eas build --platform android --profile preview", "preview:status": "npx eas build:list --limit=5", "preview:add-device": "npx eas device:create", "preview:share": "npx eas build:list --limit=2 --non-interactive", "build:production": "eas build --platform all", "build:production:ios": "eas build --platform ios", "submit:production:ios": "eas submit --platform ios", "production:status": "npx eas build:list --limit=5 --non-interactive"}, "dependencies": {"@expo/plist": "^0.3.4", "@expo/image-utils": "^0.7.4", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "53.0.11", "expo-blur": "~14.1.5", "expo-camera": "~16.1.3", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.0", "expo-file-system": "^18.1.11", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-media-library": "^17.1.7", "expo-router": "~5.0.6", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.14", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eas-cli": "^16.9.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-check-file": "^3.2.0", "typescript": "~5.8.3"}, "private": true}
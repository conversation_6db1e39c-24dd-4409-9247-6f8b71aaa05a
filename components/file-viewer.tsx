import * as FileSystem from 'expo-file-system';
import React, { useCallback, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Modal,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { downloadFile, getFileExtension } from '../utils/file-download';

interface FileViewerProps {
  visible: boolean;
  onClose: () => void;
  fileUrl: string;
  fileName: string;
  contentType?: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const FileViewer: React.FC<FileViewerProps> = ({
  visible,
  onClose,
  fileUrl,
  fileName,
  contentType,
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [localUri, setLocalUri] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);

  const fileExtension = getFileExtension(fileName);
  const isImage = contentType?.includes('image/') || ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension);
  const isPDF = contentType?.includes('pdf') || fileExtension === 'pdf';

  const loadFile = useCallback(async () => {
    setLoading(true);
    setError(null);
    setImageError(false);

    try {
      // Check if file is already downloaded
      const downloadDir = `${FileSystem.documentDirectory}downloads/`;
      const files = await FileSystem.readDirectoryAsync(downloadDir).catch(() => []);
      const baseName = fileName.replace(/\.[^/.]+$/, '');
      const existingFile = files.find(file => file.startsWith(baseName));

      if (existingFile) {
        const existingUri = `${downloadDir}${existingFile}`;
        const fileInfo = await FileSystem.getInfoAsync(existingUri);
        if (fileInfo.exists) {
          setLocalUri(existingUri);
          setLoading(false);
          return;
        }
      }

      // Download the file for viewing
      const result = await downloadFile(fileUrl, fileName, contentType);
      if (result.success && result.localUri) {
        setLocalUri(result.localUri);
      } else {
        setError(result.error || 'Failed to load file');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file');
    } finally {
      setLoading(false);
    }
  }, [fileUrl, fileName, contentType]);

  useEffect(() => {
    if (visible && fileUrl) {
      loadFile();
    }
  }, [visible, fileUrl, loadFile]);

  const handleDownload = async () => {
    try {
      const result = await downloadFile(fileUrl, fileName, contentType);
      if (result.success) {
        Alert.alert(
          'Download Complete',
          `${fileName} has been downloaded successfully.`,
          [{ text: 'OK', style: 'default' }]
        );
      } else {
        Alert.alert('Download Failed', result.error || 'Unable to download file');
      }
    } catch {
      Alert.alert('Download Error', 'An error occurred while downloading the file');
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading {fileName}...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>❌ {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadFile}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (!localUri) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>File not available</Text>
        </View>
      );
    }

    if (isImage && !imageError) {
      return (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: localUri }}
            style={styles.image}
            resizeMode="contain"
            onError={() => setImageError(true)}
            onLoad={() => setLoading(false)}
          />
        </View>
      );
    }

    if (isPDF) {
      return (
        <WebView
          source={{ uri: localUri }}
          style={styles.webview}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={styles.centerContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <Text style={styles.loadingText}>Loading PDF...</Text>
            </View>
          )}
        />
      );
    }

    // Fallback for unsupported file types
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.fileName}>{fileName}</Text>
        <Text style={styles.fileInfo}>
          File type: {contentType || 'Unknown'}
        </Text>
        <Text style={styles.fileInfo}>
          This file type cannot be previewed in the app.
        </Text>
        <TouchableOpacity style={styles.downloadButton} onPress={handleDownload}>
          <Text style={styles.downloadButtonText}>Download File</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
          
          <View style={styles.headerTitle}>
            <Text style={styles.headerTitleText} numberOfLines={1}>
              {fileName}
            </Text>
            {contentType && (
              <Text style={styles.headerSubtitle}>{contentType}</Text>
            )}
          </View>
          
          <TouchableOpacity style={styles.downloadButton} onPress={handleDownload}>
            <Text style={styles.downloadButtonText}>Download</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <View style={styles.content}>
          {renderContent()}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  closeButton: {
    padding: 8,
    marginRight: 12,
  },
  closeButtonText: {
    color: '#333333',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerTitle: {
    flex: 1,
    marginRight: 12,
  },
  headerTitleText: {
    color: '#333333',
    fontSize: 16,
    fontWeight: '600',
  },
  headerSubtitle: {
    color: '#666666',
    fontSize: 12,
    marginTop: 2,
  },
  content: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
  },
  loadingText: {
    color: '#333333',
    fontSize: 16,
    marginTop: 12,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#F76918',
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
  },
  image: {
    width: screenWidth,
    height: screenHeight - 100, // Account for header
  },
  webview: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  fileName: {
    color: '#333333',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  fileInfo: {
    color: '#666666',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 4,
  },
  downloadButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: '#F76918',
    borderRadius: 8,
  },
  downloadButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
});

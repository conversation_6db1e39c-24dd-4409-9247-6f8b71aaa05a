#!/usr/bin/env node

/**
 * Test OAuth Configuration for Multiplier App
 * This script helps verify OAuth setup and identify issues
 */

console.log('🔍 Testing OAuth Configuration for Multiplier App\n');

// Test URLs that should be allowed
const testUrls = [
  'https://accounts.google.com/gsi/select?client_id=**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com&ux_mode=popup&ui_mode=card',
  'https://accounts.google.com/oauth/authorize?client_id=test',
  'https://google.com/gsi/select',
  'https://**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com/oauth',
  'https://app.usemultiplier.com/login',
  'https://app.usemultiplier.com/dashboard',
];

console.log('📋 Testing URL Allowance Logic:\n');

testUrls.forEach(url => {
  const isGoogleOAuth = url.includes('accounts.google.com') || 
                       url.includes('google.com/oauth') || 
                       url.includes('google.com/gsi') ||
                       url.includes('googleusercontent.com');
  
  const isOAuth = url.toLowerCase().includes('oauth') || 
                  url.toLowerCase().includes('auth') || 
                  url.toLowerCase().includes('login') || 
                  url.toLowerCase().includes('signin');
  
  const isSameDomain = url.includes('usemultiplier.com');
  const isHttps = url.startsWith('https://');
  
  console.log(`URL: ${url}`);
  console.log(`  - Google OAuth: ${isGoogleOAuth ? '✅' : '❌'}`);
  console.log(`  - OAuth: ${isOAuth ? '✅' : '❌'}`);
  console.log(`  - Same Domain: ${isSameDomain ? '✅' : '❌'}`);
  console.log(`  - HTTPS: ${isHttps ? '✅' : '❌'}`);
  console.log(`  - Should Allow: ${(isGoogleOAuth || isOAuth || isSameDomain || isHttps) ? '✅' : '❌'}\n`);
});

// Check WebView configuration
console.log('📱 WebView Configuration Check:\n');

const webViewConfig = {
  userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1 Multiplier/1.0',
  allowsInlineMediaPlayback: true,
  allowsBackForwardNavigationGestures: false,
  allowsLinkPreview: false,
  incognito: false,
  allowsProtectedMedia: true,
  sharedCookiesEnabled: true,
  domStorageEnabled: true,
  javaScriptEnabled: true,
  thirdPartyCookiesEnabled: true,
  cacheEnabled: true,
  databaseEnabled: true,
};

Object.entries(webViewConfig).forEach(([key, value]) => {
  console.log(`${key}: ${value}`);
});

console.log('\n🔧 OAuth JavaScript Injection Check:\n');
console.log('✅ window.open override for OAuth popups');
console.log('✅ Google One Tap popup to redirect conversion');
console.log('✅ Google OAuth2 popup to redirect conversion');
console.log('✅ OAuth form submission monitoring');
console.log('✅ OAuth iframe load monitoring');
console.log('✅ OAuth message handling');

console.log('\n🚨 Common Issues and Solutions:\n');
console.log('1. Popup Mode: Google SSO uses popup mode which doesn\'t work in WebViews');
console.log('   Solution: ✅ Convert popup to redirect mode (implemented)');
console.log('');
console.log('2. User Agent: WebView user agent might be blocked by Google');
console.log('   Solution: ✅ Use Safari-like user agent (implemented)');
console.log('');
console.log('3. Cookies: OAuth requires cookies to work properly');
console.log('   Solution: ✅ Enable sharedCookiesEnabled and thirdPartyCookiesEnabled (implemented)');
console.log('');
console.log('4. Navigation: OAuth URLs might be blocked by navigation policy');
console.log('   Solution: ✅ Allow Google OAuth URLs (implemented)');
console.log('');
console.log('5. JavaScript: OAuth flows require JavaScript');
console.log('   Solution: ✅ Enable javaScriptEnabled (implemented)');

console.log('\n🎯 Next Steps:\n');
console.log('1. Test the updated app on device');
console.log('2. Check console logs for OAuth-related messages');
console.log('3. Verify that popup mode is converted to redirect mode');
console.log('4. Ensure Google OAuth URLs are being allowed');
console.log('5. Check if cookies are being properly handled');

console.log('\n📊 Debug Commands:\n');
console.log('To monitor OAuth flow:');
console.log('  npx expo run:ios --device');
console.log('  # Then check console logs for 🔐 messages'); 
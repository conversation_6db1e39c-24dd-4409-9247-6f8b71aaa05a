#!/usr/bin/env node

/**
 * Test script to verify OAuth configuration
 * Run this script to check if OAuth URLs are being properly detected
 */

const testUrls = [
  'https://accounts.google.com/gsi/select?client_id=test',
  'https://google.com/oauth/authorize?client_id=test',
  'https://app.usemultiplier.com/login',
  'https://app.usemultiplier.com/dashboard',
  'https://malicious-site.com/fake-oauth',
  'https://accounts.google.com/signin',
  'https://oauth.example.com/callback',
];

console.log('🔍 Testing OAuth URL detection...\n');

testUrls.forEach(url => {
  const isOAuth = url.toLowerCase().includes('accounts.google.com') ||
                  url.toLowerCase().includes('google.com/oauth') ||
                  url.toLowerCase().includes('oauth') ||
                  url.toLowerCase().includes('auth') ||
                  url.toLowerCase().includes('login') ||
                  url.toLowerCase().includes('signin');
  
  console.log(`${isOAuth ? '✅' : '❌'} ${url}`);
});

console.log('\n📋 OAuth Flow Debugging Tips:');
console.log('1. Check that Google OAuth URLs are being allowed in WebView');
console.log('2. Verify that popup mode is being converted to redirect mode');
console.log('3. Ensure OAuth redirects are being handled properly');
console.log('4. Check that cookies and storage are enabled for OAuth');
console.log('5. Verify that the web app is configured for mobile OAuth flows'); 
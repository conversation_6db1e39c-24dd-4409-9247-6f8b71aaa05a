#!/usr/bin/env node

/**
 * OAuth Debug Script for Multiplier App
 * Use this to monitor and troubleshoot OAuth flows
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 OAuth Debug Script for Multiplier App\n');

// Check app configuration
console.log('📱 App Configuration:');
console.log('- Scheme: multiplier (production)');
console.log('- Base URL: https://app.usemultiplier.com');
console.log('- OAuth URLs: accounts.google.com, google.com/oauth\n');

// Check OAuth detection logic
console.log('🔐 OAuth URL Detection Test:');
const testUrls = [
  'https://accounts.google.com/gsi/select?client_id=**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com&ux_mode=popup&ui_mode=card',
  'https://accounts.google.com/oauth/authorize',
  'https://app.usemultiplier.com/login',
  'https://app.usemultiplier.com/dashboard',
];

testUrls.forEach(url => {
  const isOAuth = url.toLowerCase().includes('accounts.google.com') ||
                  url.toLowerCase().includes('google.com/oauth') ||
                  url.toLowerCase().includes('oauth') ||
                  url.toLowerCase().includes('auth') ||
                  url.toLowerCase().includes('login') ||
                  url.toLowerCase().includes('signin');
  
  console.log(`${isOAuth ? '✅' : '❌'} ${url}`);
});

console.log('\n📋 Debugging Checklist:');
console.log('1. ✅ OAuth URLs are being detected correctly');
console.log('2. ✅ WebView navigation allows OAuth URLs');
console.log('3. ✅ Popup mode is converted to redirect mode');
console.log('4. ✅ OAuth messages are being handled');
console.log('5. ⚠️  Check web app OAuth configuration');
console.log('6. ⚠️  Verify Google OAuth client settings');
console.log('7. ⚠️  Ensure redirect URIs include app scheme');

console.log('\n🚨 Common Issues & Solutions:');
console.log('Issue: Blank page after Google login');
console.log('Solution: Check web app OAuth redirect configuration');
console.log('');
console.log('Issue: OAuth popup not working');
console.log('Solution: We\'ve converted popup to redirect mode');
console.log('');
console.log('Issue: Navigation blocked');
console.log('Solution: OAuth URLs are now explicitly allowed');
console.log('');
console.log('Issue: Cookies not persisting');
console.log('Solution: Cookie and storage settings are enabled');

console.log('\n📱 Testing Instructions:');
console.log('1. Open the app on your device');
console.log('2. Navigate to login page');
console.log('3. Try Google SSO login');
console.log('4. Watch console logs for OAuth messages');
console.log('5. Check if redirect works properly');

console.log('\n🔧 If still not working:');
console.log('1. Check web app logs for OAuth errors');
console.log('2. Verify Google OAuth client configuration');
console.log('3. Ensure redirect URIs are correct');
console.log('4. Test with different OAuth flow (redirect vs popup)');
console.log('5. Check if web app supports mobile OAuth flows'); 
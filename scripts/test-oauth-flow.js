#!/usr/bin/env node

/**
 * Test OAuth Flow for Multiplier App
 * This script helps verify the OAuth flow and provides debugging information
 */

console.log('🔍 Testing OAuth Flow for Multiplier App\n');

// Test the specific URLs from your logs
const testUrls = [
  'https://accounts.google.com/gsi/select?client_id=**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com&ux_mode=popup&ui_mode=bottom_sheet&as=MDLUbdv4V9oE-5GuMXrcB0FW4Y1Xy0CP9UtQBIsdw5s&channel_id=3a816db50bbab04e26a7d1c714137b6ab9f7a33af09c306acf3ceb110dfaf89b&origin=https%3A%2F%2Fapp.usemultiplier.com',
  'https://accounts.google.com/o/oauth2/v2/auth?as=MDLUbdv4V9oE-5GuMXrcB0FW4Y1Xy0CP9UtQBIsdw5s&client_id=**********-uvraiav59d5eepp0esdssc54c324k2um.apps.googleusercontent.com&scope=openid%20email%20profile&response_type=id_token&gsiwebsdk=gis_attributes&redirect_uri=gis_transform&response_mode=form_post&origin=https%3A%2F%2Fapp.usemultiplier.com&display=popup&prompt=select_account&gis_params=Ch1odHRwczovL2FwcC51c2VtdWx0aXBsaWVyLmNvbRINZ2lzX3RyYW5zZm9ybRgHKitNRExVYmR2NFY5b0UtNUd1TVhyY0IwRlc0WTFYeTBDUDlVdFFCSXNkdzVzMkYyMDA0Mzg3MDk1LXV2cmFpYXY1OWQ1ZWVwcDBlc2Rzc2M1NGMzMjRrMnVtLmFwcHMuZ29vZ2xldXNlcmNvbnRlbnQuY29tOAFCQDNhODE2ZGI1MGJiYWIwNGUyNmE3ZDFjNzE0MTM3YjZhYjlmN2EzM2FmMDljMzA2YWNmM2NlYjExMGRmYWY4OWI'
];

console.log('📋 Testing URL Modification Logic:\n');

testUrls.forEach((url, index) => {
  console.log(`Test URL ${index + 1}:`);
  console.log(`Original: ${url}`);
  
  let modifiedUrl = url;
  const modifications = [];
  
  // Test ux_mode conversion
  if (url.includes('ux_mode=popup')) {
    modifiedUrl = modifiedUrl.replace('ux_mode=popup', 'ux_mode=redirect');
    modifications.push('ux_mode: popup → redirect');
  }
  
  // Test display conversion
  if (url.includes('display=popup')) {
    modifiedUrl = modifiedUrl.replace('display=popup', 'display=page');
    modifications.push('display: popup → page');
  }
  
  // Test ui_mode conversion
  if (url.includes('ui_mode=bottom_sheet')) {
    modifiedUrl = modifiedUrl.replace('ui_mode=bottom_sheet', 'ui_mode=card');
    modifications.push('ui_mode: bottom_sheet → card');
  }
  
  // Test redirect_uri conversion
  if (url.includes('redirect_uri=gis_transform')) {
    modifiedUrl = modifiedUrl.replace('redirect_uri=gis_transform', 'redirect_uri=https://app.usemultiplier.com');
    modifications.push('redirect_uri: gis_transform → https://app.usemultiplier.com');
  }
  
  console.log(`Modified: ${modifiedUrl}`);
  console.log(`Changes: ${modifications.length > 0 ? modifications.join(', ') : 'None'}`);
  console.log(`Modified: ${modifiedUrl !== url ? '✅' : '❌'}\n`);
});

console.log('🔧 Expected Log Messages:\n');
console.log('When OAuth flow starts, you should see:');
console.log('🔐 Processing Google OAuth URL: [URL]');
console.log('🔐 Converted ux_mode from popup to redirect');
console.log('🔐 Converted display from popup to page');
console.log('🔐 Converted ui_mode from bottom_sheet to card');
console.log('🔐 Converted redirect_uri from gis_transform to app.usemultiplier.com');
console.log('🔐 Navigating to modified OAuth URL: [MODIFIED_URL]');

console.log('\n🎯 Testing Steps:\n');
console.log('1. Open the updated app on your device');
console.log('2. Navigate to the login page');
console.log('3. Click on Google SSO login');
console.log('4. Monitor the console logs for the messages above');
console.log('5. Check if the OAuth flow completes successfully');

console.log('\n🚨 If Still Blank Page:\n');
console.log('1. Check if you see the "🔐 Processing Google OAuth URL" message');
console.log('2. Verify that URL modifications are being applied');
console.log('3. Check if the modified URL is being navigated to');
console.log('4. Look for any error messages in the console');
console.log('5. Try with a different Google account');

console.log('\n📊 Debug Commands:\n');
console.log('To monitor logs in real-time:');
console.log('  npx expo logs');
console.log('');
console.log('To run the app with debugging:');
console.log('  npx expo run:ios --device');
console.log('');
console.log('To test OAuth configuration:');
console.log('  node scripts/test-oauth-config.js');

console.log('\n🔍 Key Changes Made:\n');
console.log('✅ URL-level popup to redirect conversion');
console.log('✅ ui_mode bottom_sheet to card conversion');
console.log('✅ redirect_uri gis_transform to app.usemultiplier.com conversion');
console.log('✅ Enhanced JavaScript injection for Google One Tap');
console.log('✅ More aggressive OAuth handling');

console.log('\n📞 Next Steps:\n');
console.log('1. Test the updated app');
console.log('2. Share the new console logs');
console.log('3. Report if the blank page issue is resolved');
console.log('4. If still blank, share any error messages'); 
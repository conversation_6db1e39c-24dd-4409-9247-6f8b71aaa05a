import { ConfigContext, ExpoConfig } from "expo/config";

// EAS project config
const EAS_PROJECT_ID = "191296a0-264e-4278-9908-b8a7cf78239a";
const PROJECT_SLUG = "multiplier-app";
const OWNER = "multiplier";

// App production config
const APP_NAME = "Multiplier";
const BUNDLE_IDENTIFIER = "com.multiplier.multiplierapp";
const PACKAGE_NAME = "com.multiplier.multiplierapp";
const ICON = "./assets/images/icon.png";
const ADAPTIVE_ICON = "./assets/images/adaptive-icon.png";
const SCHEME = "multiplier";

export default ({ config }: ConfigContext): ExpoConfig => {
  console.log("⚙️ Building app for environment:", process.env.APP_ENV);
  const { name, bundleIdentifier, icon, adaptiveIcon, packageName, scheme } =
    getDynamicAppConfig(
      (process.env.APP_ENV as "development" | "preview" | "production") ||
        "development"
    );

  return {
    ...config,
    name: name,
    version: "1.0.0",
    slug: PROJECT_SLUG, // Must be consistent across all environments.
    orientation: "portrait",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    icon: icon,
    scheme: scheme,
    ios: {
      requireFullScreen: false,
      supportsTablet: true,
      bundleIdentifier: bundleIdentifier,
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
        UIViewControllerBasedStatusBarAppearance: false,
        UIStatusBarStyle: "UIStatusBarStyleDefault",
        CFBundleURLTypes: [
          {
            CFBundleURLName: bundleIdentifier,
            CFBundleURLSchemes: [scheme],
          },
        ],
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true,
          NSAllowsLocalNetworking: true,
        },
        // Camera and Media Permissions - Required for WebView file uploads
        NSCameraUsageDescription: "This app needs access to your camera to take photos for file uploads in the web application and third-party integrations.",
        NSPhotoLibraryUsageDescription: "This app needs access to your photo library to select photos for file uploads in the web application and third-party integrations.",
        NSPhotoLibraryAddUsageDescription: "This app needs access to save photos to your photo library when downloading images from the web application.",
        NSMicrophoneUsageDescription: "This app needs access to your microphone to record audio or video for file uploads in the web application and third-party integrations.",
        // Additional permissions that might be needed
        NSDocumentPickerUsageDescription: "This app needs access to your documents to upload files in the web application and third-party integrations.",
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: adaptiveIcon,
        backgroundColor: "#ffffff",
      },
      package: packageName,
      edgeToEdgeEnabled: true,
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.READ_MEDIA_IMAGES",
        "android.permission.READ_MEDIA_VIDEO",
        "android.permission.READ_MEDIA_AUDIO",
      ],
    },
    updates: {
      url: `https://u.expo.dev/${EAS_PROJECT_ID}`,
    },
    runtimeVersion: {
      policy: "appVersion",
    },
    extra: {
      eas: {
        projectId: EAS_PROJECT_ID,
      },
      webViewUrl:
        process.env.EXPO_PUBLIC_WEB_VIEW_URL ||
        "https://app.usemultiplier.com/",
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          image: "./assets/images/splash-icon.png",
          imageWidth: 200,
          resizeMode: "contain",
          backgroundColor: "#f76918",
        },
      ],
      [
        "expo-camera",
        {
          cameraPermission: "Allow $(PRODUCT_NAME) to access your camera to take photos for file uploads in the web application and third-party integrations.",
          microphonePermission: "Allow $(PRODUCT_NAME) to access your microphone to record audio or video for file uploads in the web application and third-party integrations.",
          recordAudioAndroid: true,
        },
      ],
      [
        "expo-media-library",
        {
          photosPermission: "Allow $(PRODUCT_NAME) to access your photo library to select photos for file uploads in the web application and third-party integrations.",
          savePhotosPermission: "Allow $(PRODUCT_NAME) to save photos to your photo library when downloading images from the web application.",
          isAccessMediaLocationEnabled: true,
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    owner: OWNER,
  };
};

// Dynamically configure the app based on the environment.
// Update these placeholders with your actual values.
export const getDynamicAppConfig = (
  environment: "development" | "preview" | "production"
) => {
  if (environment === "production") {
    return {
      name: APP_NAME,
      bundleIdentifier: BUNDLE_IDENTIFIER,
      packageName: PACKAGE_NAME,
      icon: ICON,
      adaptiveIcon: ADAPTIVE_ICON,
      scheme: SCHEME,
    };
  }

  if (environment === "preview") {
    return {
      name: `${APP_NAME} Preview`,
      bundleIdentifier: `${BUNDLE_IDENTIFIER}.preview`,
      packageName: `${PACKAGE_NAME}.preview`,
      icon: "./assets/images/icons/iOS-Prev.png",
      adaptiveIcon: "./assets/images/icons/Android-Prev.png",
      scheme: `${SCHEME}-prev`,
    };
  }

  return {
    name: `${APP_NAME} Development`,
    bundleIdentifier: `${BUNDLE_IDENTIFIER}.dev`,
    packageName: `${PACKAGE_NAME}.dev`,
    icon: "./assets/images/icons/iOS-Dev.png",
    adaptiveIcon: "./assets/images/icons/Android-Dev.png",
    scheme: `${SCHEME}-dev`,
  };
};

# Welcome to Multiplier App 👋

## Overview

Multiplier App is a React Native application that serves as a mobile wrapper for the Multiplier web application. It uses WebView to provide a native mobile experience while maintaining the functionality of the web platform.

### Navigation Features

The app includes comprehensive navigation support with the following features:

- **Back Gesture Support**: Swipe from left edge to go back (iOS) or use hardware back button (Android)
- **WebView Navigation History**: Full support for browser-like navigation within the WebView
- **Haptic Feedback**: Tactile feedback when navigating back
- **Deep Link Integration**: Seamless navigation from external links
- **Cross-Platform**: Works consistently across iOS and Android

### Tech Stack

- **Framework**: React Native with Expo
- **Navigation**: Expo Router
- **Web Integration**: React Native WebView
- **UI Components**:
  - Expo Vector Icons
  - Expo Blur
  - Expo Image
- **State Management**: React Native's built-in state management
- **Development Tools**:
  - TypeScript
  - ESLint
  - Yarn (Package Manager)

## Package Management

This project uses Yarn 3 (<PERSON>) with the following configuration:

- Uses `node-modules` linker (configured in `.yarnrc.yml`)
- Zero-installs enabled for faster dependency installation
- Plugins and patches are tracked in version control

### Yarn Cache and Lockfile

- The project uses Yarn 3's sophisticated caching system
- The `yarn.lock` file is more detailed than Yarn 1.x and includes additional metadata
- Cache files are ignored in `.gitignore` but essential Yarn files are tracked
- If you encounter any issues with dependencies, try:
  ```bash
  yarn cache clean
  yarn install
  ```

## Local Development Setup

### Prerequisites

Before setting up the project locally, ensure you have the following installed:
- Node.js (LTS version recommended)
- Yarn package manager (v3.x)
- iOS Simulator (for iOS development)
- Android Studio & Android SDK (for Android development)
- Expo CLI (`npm install -g expo-cli`)
- EAS CLI (for environment variables)

### Getting Started

Follow these steps to set up the project locally:

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd multiplier-app
   ```

2. **⚠️ IMPORTANT: Pull Environment Variables**

   **This step is required before installing dependencies or starting the app.**

   The app requires environment variables from EAS (Expo Application Services) to run properly. You must pull these variables to create the required `.env.local` file:

   ```bash
   eas env:pull --environment development
   ```

   **What this does:**
   - Downloads environment variables from EAS for the development environment
   - Creates a `.env.local` file in your project root
   - Provides necessary configuration values (API endpoints, keys, etc.)
   - **Without this file, the app will not start properly**

   **Note:** The `.env.local` file is automatically ignored by Git for security reasons.

3. **Install Dependencies**
   ```bash
   yarn
   ```

4. **Configure WebView URL (Optional)**
   The WebView URL is dynamically configured based on build profile in `app.config.js`. Each environment has its own URL:

    // Current environment URLs:

    - Development: "https://dev-customer.frontend.acc.staging.usemultiplier.com/"
    - Preview: "https://app.usemultiplier.com/"
    - Production: "https://app.usemultiplier.com/"

    ````

    **For local development**, temporarily set your local server:
    ```bash
    export WEB_VIEW_URL="http://localhost:3000"
    yarn start
    ````

    Note:

    - For iOS simulator, use `localhost`
    - For Android emulator, use `********` instead of `localhost`

4.  **Start the Development Server**

    ```bash
    yarn start
    ```

    This will start the Expo development server. You can then:

   Note:
   - For iOS simulator, use `localhost`
   - For Android emulator, use `********` instead of `localhost`

5. **Start the Development Server**
   ```bash
   yarn start
   ```

   This will start the Expo development server. You can then:
   - Press `i` to open in iOS simulator
   - Press `a` to open in Android emulator
   - Scan the QR code with Expo Go app on your physical device

## Building the App

### iOS Build and Run

1. **Create Development Build**

   ```bash
   # Install pods first
   cd ios
   pod install
   cd ..

   # Create development build
   yarn ios
   ```

   If you encounter any issues, try:

   ```bash
   # Clean the build
   cd ios
   xcodebuild clean
   cd ..

   # Remove pods and reinstall
   cd ios
   rm -rf Pods
   rm -rf Podfile.lock
   pod install
   cd ..

   # Try building again
   yarn ios
   ```

2. **Run on iOS Simulator**
   ```bash
   yarn start
   # Then press 'i' to open in iOS simulator
   ```

### Android Build

```bash
yarn android
```

## Project Structure

```
multiplier-app/
├── app/                 # Main application code
│   └── _layout.tsx     # Root layout with WebView implementation
├── assets/             # Static assets
├── ios/               # iOS native code
├── android/           # Android native code
└── scripts/           # Build and utility scripts
```

## 📱 Preview Builds with EAS Internal Distribution

### Prerequisites for Preview Testing

1. **EAS CLI** - Already installed as a dev dependency in this project
2. **Device Registration** - iOS devices must be registered in Apple Developer account
3. **Android APK Installation** - Testers need to enable "Install from Unknown Sources"

### 📱 Preview Distribution Process

**Simple 4-step process for preview testing:**

#### Step 1: Register Device (iOS Only)

```bash
yarn preview:add-device
```

- Scan the QR code on the testing device
- Install the profile when prompted
- Device is automatically registered

#### Step 2: Build Preview

```bash
yarn preview:build    # Builds both iOS & Android
```

Wait ~15 minutes for build to complete.

#### Step 3: Share Build URLs

```bash
yarn preview:share    # Get download links
```

Send the download URLs to your team.

#### Step 4: Installation

**iOS:**

1. Open download link on iOS device
2. Install the app
3. Settings → General → VPN & Device Management → Trust developer
4. Enable Developer Mode (iOS 16+): Settings → Privacy & Security → Developer Mode
5. Launch the app

**Android:**

1. Download the APK file
2. Enable "Install from Unknown Sources" if prompted
3. Install and launch

### 🔄 Preview Commands Reference

```bash
yarn preview:build        # Build both iOS & Android
yarn preview:status       # Check build progress
yarn preview:share        # Get download URLs
yarn preview:add-device   # Register new device
```

### ⚠️ Important Notes

- **iOS Limitation**: 100 devices max per Apple Developer account per year
- **Device Registration**: Required before building, not after
- **Build Time**: ~15 minutes for both platforms
- **Android**: No device registration needed

### 📋 Preview Environment

- **Environment**: Staging (`https://app.usemultiplier.com/`)
- **Distribution**: Internal (no store approval)
- **iOS Build Numbers**: Auto-increment
- **Build Expiry**: iOS builds expire after 7 days

## 🚀 Production Store Distribution

### Prerequisites for Production

1. **Apple Developer Account** - Active membership ($99/year)
2. **App Store Connect** - App record created and configured
3. **Google Play Console** - Developer account for Android ($25 one-time)
4. **EAS CLI Authentication** - `npx eas login`

### 📱 Production Release Process

**Step-by-step production workflow:**

#### Step 1: Pre-Release Checks

```bash
# Ensure everything is clean
yarn lint

# Check current build status
yarn production:status
```

#### Step 2: Build for Production

```bash
# Build both iOS & Android (recommended)
yarn build:production

# Or build specific platform
yarn build:production:ios    # iOS only
```

#### Step 3: Submit to Stores

```bash
# Submit iOS to App Store (automated)
yarn submit:production

# Android: Manual upload to Google Play Console
# Download APK from EAS and upload via console
```

### 🔄 Production Commands Reference

```bash
yarn build:production        # Build both iOS & Android
yarn build:production:ios    # Build iOS only
yarn submit:production       # Submit iOS to App Store
yarn production:status       # Check build status
```

### 📋 Production Environment

- **Environment**: Production (`https://app.usemultiplier.com/`)
- **Distribution**: Store (App Store + Google Play)
- **Build Configuration**: Release mode
- **Auto-increment**: Both iOS and Android version codes

### ⚠️ Production Notes

- **iOS**: Automated submission to App Store
- **Android**: Manual upload to Google Play Console required
- **Build Time**: ~15-20 minutes for both platforms
- **Review Process**: 24-48 hours for App Store, 2-3 hours for Google Play

## 🧪 TestFlight Beta Distribution

### Quick TestFlight Commands

```bash
# Build for TestFlight
yarn testflight:build

# Submit to TestFlight
yarn testflight:submit

# Build and auto-submit (most common)
yarn testflight:build-submit
```

### TestFlight Configuration

The app is configured with a dedicated `testflight` profile in `eas.json`:

- **Distribution**: App Store Connect
- **Environment**: Production (`https://app.usemultiplier.com/`)
- **Auto-increment**: iOS build numbers
- **Team ID**: `9H5UAC5W2D`
- **App ID**: `6747017048`

### TestFlight Workflow

1. **Build**: `yarn testflight:build-submit` (builds and submits automatically)
2. **Processing**: Wait ~15-20 minutes for build + App Store processing
3. **Testing**: Build appears in TestFlight app for internal testers
4. **Distribution**: Add testers in [App Store Connect](https://appstoreconnect.apple.com/)

**Note**: Internal testers can access builds immediately. External testers require Apple's beta review (first time only).

## 🔗 Deep Linking

The app supports flexible deep linking to navigate directly to any route in the web application. Deep links are automatically converted to the corresponding web URLs.

### Deep Link Format

**Basic Format:** `multiplier://path` (environment-specific schemes: `multiplier-dev://`, `multiplier-prev://`)

The app supports any URL path and query parameters:

| Deep Link                                        | Web URL                              | Description                      |
| ------------------------------------------------ | ------------------------------------ | -------------------------------- |
| `multiplier://dashboard`                         | `/dashboard`                         | Basic route                      |
| `multiplier://expenses/reports`                  | `/expenses/reports`                  | Nested route                     |
| `multiplier://dashboard?tab=analytics`           | `/dashboard?tab=analytics`           | With query params                |
| `multiplier://expenses/reports/2024?filter=paid` | `/expenses/reports/2024?filter=paid` | Complex nested route with params |

### Android Configuration

For Android deep linking to work properly, add the following intent filters to your `android/app/src/main/AndroidManifest.xml`:

```xml
<activity
  android:name=".MainActivity"
  android:launchMode="singleTask">

  <!-- Existing activity configuration -->
  <intent-filter>
    <action android:name="android.intent.action.MAIN" />
    <category android:name="android.intent.category.LAUNCHER" />
  </intent-filter>

  <!-- Deep Link Intent Filter -->
  <intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="multiplier" />
  </intent-filter>

  <!-- Development Deep Link -->
  <intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="multiplier-dev" />
  </intent-filter>

  <!-- Preview Deep Link -->
  <intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="multiplier-prev" />
  </intent-filter>

</activity>
```

### Universal Links Setup (Recommended for Production)

For production apps, implement Universal Links to support HTTPS deep links:

**iOS Setup:**

1. Add Associated Domains to your app configuration:

```typescript
// In app.config.ts
ios: {
  // ... existing config
  associatedDomains: ["applinks:app.usemultiplier.com"];
}
```

2. Host Apple App Site Association (AASA) file at:
   `https://app.usemultiplier.com/.well-known/apple-app-site-association`

**Android Setup:**

1. Add App Links intent filter with domain verification:

```xml
<intent-filter android:autoVerify="true">
  <action android:name="android.intent.action.VIEW" />
  <category android:name="android.intent.category.DEFAULT" />
  <category android:name="android.intent.category.BROWSABLE" />
  <data android:scheme="https"
        android:host="app.usemultiplier.com" />
</intent-filter>
```

2. Host Digital Asset Links file at:
   `https://app.usemultiplier.com/.well-known/assetlinks.json`

### Testing Deep Links

**iOS Simulator:**

```bash
# Using npx uri-scheme (recommended)
npx uri-scheme open "multiplier://dashboard" --ios

# Using xcrun simctl
xcrun simctl openurl booted "multiplier://dashboard"
```

**Android Emulator:**

```bash
# Using npx uri-scheme (recommended)
npx uri-scheme open "multiplier://dashboard" --android

# Using adb
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "multiplier://dashboard" \
  com.multiplier.multiplierapp
```

**Testing Different Environments:**

```bash
# Development
npx uri-scheme open "multiplier-dev://expenses/reports?filter=pending" --ios

# Preview
npx uri-scheme open "multiplier-prev://dashboard?tab=analytics" --android

# Production
npx uri-scheme open "multiplier://profile/settings" --ios
```

**Development Console Debugging:**
Deep link events and navigation are automatically logged to the console in development mode with emojis for easy identification:

- 🚀 App launched with deep link
- 🔗 Deep link received while running
- 📍 Successful navigation
- ⚠️ Parsing warnings
- ❌ Critical errors
- 🚫 Blocked navigations

### Implementation Details

Deep linking is implemented using:

- **Core utilities**: `utils/deep-linking.ts` - URL parsing, validation, and WebView URL construction
- **Main integration**: `app/_layout.tsx` - Deep link event handling and WebView navigation
- **Security**: Scheme validation and domain restrictions for WebView navigation

**Key Features:**

- Environment-aware scheme handling (dev/preview/production)
- Robust URL parsing with query parameter support
- Comprehensive error handling and development logging
- WebView security with same-domain restrictions
- Support for both cold starts and warm navigation

The system handles both cold starts (app closed) and warm navigation (app running) scenarios with comprehensive logging for debugging.

## 📸 Camera and File Upload Configuration

### iOS Camera Permissions

The app includes comprehensive camera and media library permissions to support:
- **WebView File Uploads**: Native file picker with camera access
- **Third-party Integrations**: Camera functionality in embedded services (e.g., Intercom)
- **Image Downloads**: Saving images to photo library

### Required Permissions

The following iOS permissions are configured in `app.config.ts`:

- **NSCameraUsageDescription**: Camera access for photo/video capture
- **NSPhotoLibraryUsageDescription**: Photo library access for file selection
- **NSPhotoLibraryAddUsageDescription**: Permission to save downloaded images
- **NSMicrophoneUsageDescription**: Microphone access for video recording
- **NSDocumentPickerUsageDescription**: Document picker access for file uploads

### Android Permissions

Android permissions are automatically configured through the `expo-camera` and `expo-media-library` plugins and added to your app configuration:

```xml
<!-- Automatically added by Expo plugins -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

**Note:** These permissions support both legacy Android versions (<13) and modern scoped storage (Android 13+). The app will automatically request runtime permissions when users first attempt to use camera or file upload features.

**Platform-Specific Behaviors:**

**iOS:**
- Single permission prompt covers camera, microphone, and photo library
- Photos saved to app's document directory and optionally to Photos app
- Native iOS file picker with camera integration

**Android:**
- Granular permission requests (Camera, Storage, Microphone separately)
- Photos saved to app's internal storage with optional external storage
- Android's native file chooser with camera integration
- Supports both legacy storage (Android <13) and scoped storage (Android 13+)

### Troubleshooting

**If camera still crashes after configuration:**

1. **Clean and Rebuild**:
   ```bash
   # Clean the build
   yarn install
   npx expo prebuild --clean
   ```

2. **Check Permissions in Settings**:
   
   **iOS:**
   - Go to iOS Settings → Privacy & Security → Camera
   - Ensure your app has camera permission enabled

   **Android:**
   - Go to Android Settings → Apps → [Your App] → Permissions
   - Ensure Camera, Microphone, and Storage permissions are enabled
   - For Android 13+: Also check "Photos and videos" permission

3. **Test File Upload**:
   - Open app and navigate to a page with file upload
   - Tap file input and select "Camera"
   - Grant permissions when prompted

**For Development Builds:**
```bash
# iOS
npx expo run:ios --clear-cache

# Android
npx expo run:android --clear-cache
```

**For Production Builds:**
```bash
# iOS
yarn build:production:ios

# Android
yarn build:production:android
```

### Android-Specific Considerations

**Android 13+ (API 33) Permissions:**
- Granular media permissions (READ_MEDIA_IMAGES, READ_MEDIA_VIDEO, READ_MEDIA_AUDIO)
- Legacy storage permissions maintained for backward compatibility
- Runtime permission requests handled automatically by Expo plugins

**Android WebView File Upload:**
- Enhanced WebView configuration for better file access
- Mixed content support for HTTPS/HTTP file uploads
- Third-party cookie support for embedded widgets

**Android Testing Steps:**
1. **Test Camera Access**:
   ```bash
   # Launch Android app
   npx expo run:android
   # Navigate to file upload
   # Tap file input → Camera → Take photo
   ```

2. **Test Permissions**:
   - First launch should request camera permission
   - Subsequent file uploads should work without permission prompts
   - Check app permissions in Android Settings if issues persist

3. **Test Third-Party Integrations**:
   - Test Intercom file uploads
   - Test any embedded widgets with file upload
   - Verify no crashes occur when accessing camera

**Android Troubleshooting:**

If camera still doesn't work on Android:

1. **Check Android Version Compatibility**:
   ```bash
   # Ensure target SDK is appropriate
   # Check android/app/build.gradle for compileSdkVersion
   ```

2. **Manual Permission Grant**:
   - Go to Android Settings → Apps → [Your App] → Permissions
   - Manually enable Camera, Microphone, Storage
   - For Android 13+: Enable "Photos and videos"

3. **Clear App Data** (if testing):
   - Android Settings → Apps → [Your App] → Storage → Clear Storage
   - Restart app to re-request permissions

4. **Check WebView Implementation**:
   - Ensure WebView is up to date on device
   - Some older Android devices may have WebView issues

## Development Troubleshooting

### Common Issues and Solutions

- **App won't start or crashes immediately**:
  - Ensure you've run `eas env:pull --environment development` to create the `.env.local` file
  - Check that the `.env.local` file exists in your project root

- **WebView not loading**:
  - Ensure your local development server is running and accessible
  - Verify environment variables are properly configured

- **Platform-specific networking**:
  - For iOS simulator, use `localhost` instead of `127.0.0.1`
  - For Android emulator, use `********` instead of `localhost`

- **Dependency issues**:
  ```bash
  yarn cache clean
  yarn install
  ```

- **Environment variable issues**:
  ```bash
  # Re-pull environment variables if they seem outdated
  eas env:pull --environment development

  # Verify .env.local file exists and contains variables
  cat .env.local
  ```

- **Deep link issues**: Check console logs for parsing errors and verify URL scheme configuration
